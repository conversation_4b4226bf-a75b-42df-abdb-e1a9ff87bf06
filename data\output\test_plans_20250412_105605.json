[{"title": "Test Plan 1 for gate_cse", "description": "Generated test plan", "questions": "```json\n[\n    {\n        \"title\": \"Big Data Fundamentals Test 1\",\n        \"description\": \"This test covers the basic concepts of Big Data, its characteristics, types, and the need for it.\",\n        \"time_estimate\": \"60 minutes\",\n        \"questions\": [\n            {\n                \"question\": \"What are the 4 Vs of Big Data?\",\n                \"difficulty\": \"easy\",\n                \"answer\": \"Volume, Velocity, Variety, Veracity\",\n                \"explanation\": \"These four characteristics define the nature of Big Data and the challenges it presents.\"\n            },\n            {\n                \"question\": \"Which type of data lacks a predefined structure and requires schema establishment for analysis?\",\n                \"difficulty\": \"easy\",\n                \"answer\": \"Unstructured Data\",\n                \"explanation\": \"Examples include text files, images, and videos.\"\n            },\n            {\n                \"question\": \"What is the difference between vertical and horizontal scalability?\",\n                \"difficulty\": \"medium\",\n                \"answer\": \"Vertical scaling involves increasing the resources of a single system, while horizontal scaling involves adding more systems to distribute the workload.\",\n                \"explanation\": \"These are two different approaches to handling increasing data demands.\"\n            },\n            {\n                \"question\": \"What is the primary purpose of cluster computing?\",\n                \"difficulty\": \"medium\",\n                \"answer\": \"Load balancing\",\n                \"explanation\": \"Cluster computing connects computers in a network to work together, mainly for distributing workload and improving performance.\"\n            },\n            {\n                \"question\": \"List the five layers of Big Data Architecture.\",\n                \"difficulty\": \"medium\",\n                \"answer\": \"Data Source Identification, Data Ingestion and Acquisition, Data Storage, Data Processing, Data Consumption\",\n                \"explanation\": \"These layers represent the different stages in the Big Data lifecycle.\"\n            },\n            {\n                \"question\": \"What are the 5 Rs of data quality?\",\n                \"difficulty\": \"medium\",\n                \"answer\": \"Relevancy, Recency, Range, Robustness, Reliability\",\n                \"explanation\": \"These five factors determine the overall quality and usability of data.\"\n            },\n            {\n                \"question\": \"Explain the difference between grid and cluster computing.\",\n                \"difficulty\": \"hard\",\n                \"answer\": \"Grid computing connects geographically dispersed computers for a common task, focusing on data-intensive storage. Cluster computing connects computers in a network, primarily for load balancing.\",\n                \"explanation\": \"While both involve multiple computers, their purpose and architecture differ.\"\n            },\n            {\n                \"question\": \"Describe the concept of data veracity.\",\n                \"difficulty\": \"easy\",\n                \"answer\": \"Data veracity refers to the quality, trustworthiness, and accuracy of data.\",\n                \"explanation\": \"It's crucial to ensure data is reliable for making informed decisions.\"\n            },\n            {\n                \"question\": \"Give examples of multi-structured data.\",\n                \"difficulty\": \"hard\",\n                \"answer\": \"Multi-structured data combines structured, semi-structured, and unstructured data, often found in non-transactional systems.  Examples can include sensor data combined with location data and social media posts.\",\n                \"explanation\": \"This data type presents unique challenges for analysis due to its heterogeneous nature.\"\n            },\n            {\n                \"question\": \"What is the purpose of data pre-processing?\",\n                \"difficulty\": \"easy\",\n                \"answer\": \"Data pre-processing involves cleaning, editing, reducing, wrangling, and transforming data before analysis.\",\n                \"explanation\": \"It prepares data for effective data mining and analytics.\"\n            }\n\n        ]\n    },\n    {\n        \"title\": \"Big Data Platforms and Hadoop Test 2\",\n        \"description\": \"This test focuses on Big Data platforms, especially Hadoop and its ecosystem.\",\n        \"time_estimate\": \"75 minutes\",\n        \"questions\": [\n            {\n                \"question\": \"What is Hadoop?\",\n                \"difficulty\": \"easy\",\n                \"answer\": \"An open-source framework for distributed processing of large datasets.\",\n                \"explanation\": \"Hadoop is designed to handle the challenges of Big Data.\"\n            },\n             {\n                \"question\": \"What is the purpose of HDFS in Hadoop?\",\n                \"difficulty\": \"medium\",\n                \"answer\": \"HDFS (Hadoop Distributed File System) is the storage layer of Hadoop, designed for storing large files across a cluster of commodity hardware.\",\n                \"explanation\": \"It provides fault tolerance and data replication.\"\n            },\n            {\n                \"question\": \"What is the role of YARN in the Hadoop ecosystem?\",\n                \"difficulty\": \"medium\",\n                \"answer\": \"YARN (Yet Another Resource Negotiator) is the resource management framework in Hadoop, responsible for managing cluster resources and scheduling applications.\",\n                \"explanation\": \"It allows for running various processing frameworks on Hadoop besides MapReduce.\"\n            },\n             {\n                \"question\": \"Name three components of the Hadoop ecosystem.\",\n                \"difficulty\": \"easy\",\n                \"answer\": \"Any three of: HDFS, MapReduce, YARN, ZooKeeper, Pig, Hive, Sqoop, Flume, Oozie, HBase\",\n                \"explanation\": \"These components work together to provide a complete Big Data processing solution.\"\n            },\n             {\n                \"question\": \"What is the primary function of MapReduce?\",\n                \"difficulty\": \"medium\",\n                \"answer\": \"MapReduce is a programming model for processing large datasets in parallel across a distributed cluster.\",\n                \"explanation\": \"It involves two main phases: map and reduce.\"\n            },\n             {\n                \"question\": \"What is the advantage of using a Big Data platform?\",\n                \"difficulty\": \"easy\",\n                \"answer\": \"Big Data platforms support large datasets and high-velocity data processing, providing tools for storage, processing, analytics, and environment management.\",\n                \"explanation\": \"They enable organizations to extract value from their Big Data.\"\n            },\n {\n                \"question\": \"Explain the function of Hive in the Hadoop ecosystem.\",\n                \"difficulty\": \"hard\",\n                \"answer\": \"Hive provides a SQL-like interface for querying and managing data stored in HDFS. It translates SQL queries into MapReduce jobs.\",\n                \"explanation\": \"It simplifies data analysis for users familiar with SQL.\"\n            },\n{\n                \"question\": \"What is the role of Sqoop in Hadoop?\",\n                \"difficulty\": \"hard\",\n                \"answer\": \"Sqoop is a tool for transferring data between Hadoop and relational databases.\",\n                \"explanation\": \"It allows for importing data from RDBMS to HDFS and exporting data from HDFS to RDBMS.\"\n            },\n{\n                \"question\": \"Explain the function of Flume in the Hadoop ecosystem.\",\n                \"difficulty\": \"hard\",\n                \"answer\": \"Flume is a distributed, reliable, and available service for efficiently collecting, aggregating, and moving large amounts of log data.\",\n                \"explanation\": \"It is used for ingesting streaming data into HDFS.\"\n            },\n{\n                \"question\": \"What is the role of Oozie in Hadoop?\",\n                \"difficulty\": \"hard\",\n                \"answer\": \"Oozie is a workflow scheduler system to manage Hadoop jobs.\",\n                \"explanation\": \"It allows for defining and executing complex workflows involving multiple Hadoop components.\"\n            }\n\n\n\n        ]\n    },\n    {\n        \"title\": \"Big Data Applications and Data Quality Test 3\",\n        \"description\": \"This test covers applications of Big Data and data quality aspects.\",\n        \"time_estimate\": \"60 minutes\",\n        \"questions\": [\n            {\n                \"question\": \"Name two applications of Big Data in healthcare.\",\n                \"difficulty\": \"easy\",\n                \"answer\": \"Any two of: Value-based care, Fraud detection, Patient monitoring, Predictive modeling, Research\",\n                \"explanation\": \"Big Data is transforming healthcare in various ways.\"\n            },\n{\n                \"question\": \"How is Big Data used in marketing and sales?\",\n                \"difficulty\": \"easy\",\n                \"answer\": \"Examples include customer value analytics and fraud detection.\",\n                \"explanation\": \"Big Data helps businesses understand customer behavior and improve their marketing strategies.\"\n            },\n{\n                \"question\": \"What is data integrity?\",\n                \"difficulty\": \"easy\",\n                \"answer\": \"Maintaining the consistency and accuracy of data.\",\n                \"explanation\": \"It is essential for reliable data analysis.\"\n            },\n{\n                \"question\": \"What is data noise?\",\n                \"difficulty\": \"easy\",\n                \"answer\": \"Meaningless information that affects data quality.\",\n                \"explanation\": \"Data noise needs to be removed during pre-processing.\"\n            },\n{\n                \"question\": \"What are outliers in a dataset?\",\n                \"difficulty\": \"easy\",\n                \"answer\": \"Data points that fall outside the expected range.\",\n                \"explanation\": \"Outliers can skew analytical results and need to be handled carefully.\"\n            },\n            {\n                \"question\": \"Explain the concept of 'missing values' in a dataset.\",\n                \"difficulty\": \"medium\",\n                \"answer\": \"Missing values represent gaps in the dataset where information is unavailable for certain variables or observations.\",\n                \"explanation\": \"They can impact analysis and need to be addressed using techniques like imputation or deletion.\"\n            },\n            {\n                \"question\": \"What are duplicate values in a dataset, and why are they problematic?\",\n                \"difficulty\": \"medium\",\n                \"answer\": \"Duplicate values are repeated entries of the same data point. They can lead to inaccuracies in analysis and reporting, inflate dataset size unnecessarily, and skew statistical calculations.\",\n                \"explanation\": \"Identifying and removing duplicates is a crucial step in data pre-processing.\"\n            },\n            {\n                \"question\": \"Describe the challenges associated with handling high-velocity data.\",\n                \"difficulty\": \"hard\",\n                \"answer\": \"High-velocity data arrives at a rapid pace, requiring real-time or near real-time processing. This poses challenges in terms of storage capacity, processing speed, data ingestion mechanisms, and the ability to extract insights quickly enough to be actionable.\",\n                \"explanation\": \"Specialized tools and architectures are necessary to handle this type of data effectively.\"\n            },\n            {\n                \"question\": \"Explain how Big Data analytics can be used for fraud detection.\",\n                \"difficulty\": \"hard\",\n                \"answer\": \"Big Data analytics enables the analysis of massive datasets from various sources to identify patterns and anomalies indicative of fraudulent activity. Machine learning algorithms can be trained on historical data to identify suspicious transactions, behaviors, or claims, enabling proactive fraud prevention.\",\n                \"explanation\": \"By analyzing large volumes of data, organizations can detect subtle patterns that might be missed by traditional fraud detection methods.\"\n            },\n             {\n                \"question\": \"Discuss the ethical implications of using Big Data in advertising.\",\n                \"difficulty\": \"hard\",\n                \"answer\": \"While Big Data allows for targeted advertising and personalized recommendations, ethical concerns arise regarding user privacy, data security, and the potential for manipulation or discrimination.  Collecting and using personal data for advertising must be done responsibly and transparently, with users having control over their data and understanding how it is used.\",\n                \"explanation\": \"Balancing the benefits of targeted advertising with the ethical considerations of data privacy is an ongoing challenge.\"\n            }\n        ]\n    }\n]\n```"}]