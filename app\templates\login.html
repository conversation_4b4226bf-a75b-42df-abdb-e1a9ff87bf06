{% extends "base.html" %}

{% block title %}Login - Educational Content Analysis System{% endblock %}

{% block content %}
<div class="flex justify-center items-center min-h-[calc(100vh-200px)]">
    <div class="bg-white rounded-lg shadow-md p-8 w-full max-w-md">
        <h2 class="text-2xl font-bold mb-6 text-center">Login</h2>
        
        <div id="login-error" class="hidden mb-4 p-3 bg-red-100 text-red-700 rounded"></div>
        
        <form id="login-form" class="space-y-4">
            <div>
                <label for="username" class="block text-gray-700 mb-2">Username</label>
                <input type="text" id="username" name="username" class="w-full p-2 border border-gray-300 rounded" required>
            </div>
            
            <div>
                <label for="password" class="block text-gray-700 mb-2">Password</label>
                <input type="password" id="password" name="password" class="w-full p-2 border border-gray-300 rounded" required>
            </div>
            
            <div class="flex justify-between items-center">
                <button type="submit" class="bg-indigo-600 text-white px-6 py-2 rounded hover:bg-indigo-700">
                    Login
                </button>
                <a href="/register" class="text-indigo-600 hover:text-indigo-800">Create an account</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.getElementById('login-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const errorDiv = document.getElementById('login-error');
        
        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username,
                    password
                })
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                errorDiv.textContent = data.error || 'Login failed. Please try again.';
                errorDiv.classList.remove('hidden');
                return;
            }
            
            // Store token in localStorage
            localStorage.setItem('auth_token', data.access_token);
            localStorage.setItem('user_id', data.user_id);
            localStorage.setItem('username', data.username);
            localStorage.setItem('role', data.role);
            
            // Redirect to dashboard
            window.location.href = '/';
            
        } catch (error) {
            console.error('Error:', error);
            errorDiv.textContent = 'An error occurred. Please try again.';
            errorDiv.classList.remove('hidden');
        }
    });
</script>
{% endblock %}
