BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Study Plan Calendar//
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250412T090000Z
DTEND:20250412T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical equivalence\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions
DTSTART:20250412T110000Z
DTEND:20250412T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Partial orders\, lattices
DTSTART:20250412T090000Z
DTEND:20250412T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPosets\, Hasse diagrams\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Monoids\, Groups
DTSTART:20250412T110000Z
DTEND:20250412T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, subgroups\, homomorphisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Graphs
DTSTART:20250412T090000Z
DTEND:20250412T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, connectivity\, matching
 \, coloring
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Combinatorics
DTSTART:20250412T110000Z
DTEND:20250412T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCounting principles\, recurrence relations\, ge
 nerating functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250412T090000Z
DTEND:20250412T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations
DTSTART:20250412T110000Z
DTEND:20250412T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inverse
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Eigenvalues and eigenvectors
DTSTART:20250412T090000Z
DTEND:20250412T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue computation\, eigenvectors propertie
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: LU decomposition
DTSTART:20250412T110000Z
DTEND:20250412T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250412T090000Z
DTEND:20250412T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit definition\, continuity conditions\, diff
 erentiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima
DTSTART:20250412T110000Z
DTEND:20250412T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Mean value theorem
DTSTART:20250412T090000Z
DTEND:20250412T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMean value theorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Integration
DTSTART:20250412T110000Z
DTEND:20250412T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques\, definite and indefinit
 e integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables
DTSTART:20250412T090000Z
DTEND:20250412T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent types of random variables and their p
 roperties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Mean\, median\, mode\, standard
  deviation
DTSTART:20250412T110000Z
DTEND:20250412T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMeasures of central tendency and dispersion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250413T090000Z
DTEND:20250413T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical equivalence\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions
DTSTART:20250413T110000Z
DTEND:20250413T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Partial orders\, lattices
DTSTART:20250413T090000Z
DTEND:20250413T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPosets\, Hasse diagrams\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Monoids\, Groups
DTSTART:20250413T110000Z
DTEND:20250413T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, subgroups\, homomorphisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Graphs
DTSTART:20250413T090000Z
DTEND:20250413T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, connectivity\, matching
 \, coloring
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Combinatorics
DTSTART:20250413T110000Z
DTEND:20250413T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCounting principles\, recurrence relations\, ge
 nerating functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250413T090000Z
DTEND:20250413T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations
DTSTART:20250413T110000Z
DTEND:20250413T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inverse
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Eigenvalues and eigenvectors
DTSTART:20250413T090000Z
DTEND:20250413T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue computation\, eigenvectors propertie
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: LU decomposition
DTSTART:20250413T110000Z
DTEND:20250413T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250413T090000Z
DTEND:20250413T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit definition\, continuity conditions\, diff
 erentiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima
DTSTART:20250413T110000Z
DTEND:20250413T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Mean value theorem
DTSTART:20250413T090000Z
DTEND:20250413T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMean value theorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Integration
DTSTART:20250413T110000Z
DTEND:20250413T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques\, definite and indefinit
 e integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables
DTSTART:20250413T090000Z
DTEND:20250413T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent types of random variables and their p
 roperties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Mean\, median\, mode\, standard
  deviation
DTSTART:20250413T110000Z
DTEND:20250413T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMeasures of central tendency and dispersion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250414T090000Z
DTEND:20250414T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical equivalence\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions
DTSTART:20250414T110000Z
DTEND:20250414T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Partial orders\, lattices
DTSTART:20250414T090000Z
DTEND:20250414T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPosets\, Hasse diagrams\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Monoids\, Groups
DTSTART:20250414T110000Z
DTEND:20250414T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, subgroups\, homomorphisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Graphs
DTSTART:20250414T090000Z
DTEND:20250414T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, connectivity\, matching
 \, coloring
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Combinatorics
DTSTART:20250414T110000Z
DTEND:20250414T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCounting principles\, recurrence relations\, ge
 nerating functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250414T090000Z
DTEND:20250414T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations
DTSTART:20250414T110000Z
DTEND:20250414T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inverse
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Eigenvalues and eigenvectors
DTSTART:20250414T090000Z
DTEND:20250414T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue computation\, eigenvectors propertie
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: LU decomposition
DTSTART:20250414T110000Z
DTEND:20250414T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250414T090000Z
DTEND:20250414T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit definition\, continuity conditions\, diff
 erentiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima
DTSTART:20250414T110000Z
DTEND:20250414T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Mean value theorem
DTSTART:20250414T090000Z
DTEND:20250414T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMean value theorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Integration
DTSTART:20250414T110000Z
DTEND:20250414T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques\, definite and indefinit
 e integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables
DTSTART:20250414T090000Z
DTEND:20250414T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent types of random variables and their p
 roperties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Mean\, median\, mode\, standard
  deviation
DTSTART:20250414T110000Z
DTEND:20250414T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMeasures of central tendency and dispersion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250415T090000Z
DTEND:20250415T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical equivalence\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions
DTSTART:20250415T110000Z
DTEND:20250415T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Partial orders\, lattices
DTSTART:20250415T090000Z
DTEND:20250415T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPosets\, Hasse diagrams\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Monoids\, Groups
DTSTART:20250415T110000Z
DTEND:20250415T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, subgroups\, homomorphisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Graphs
DTSTART:20250415T090000Z
DTEND:20250415T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, connectivity\, matching
 \, coloring
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Combinatorics
DTSTART:20250415T110000Z
DTEND:20250415T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCounting principles\, recurrence relations\, ge
 nerating functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250415T090000Z
DTEND:20250415T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations
DTSTART:20250415T110000Z
DTEND:20250415T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inverse
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Eigenvalues and eigenvectors
DTSTART:20250415T090000Z
DTEND:20250415T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue computation\, eigenvectors propertie
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: LU decomposition
DTSTART:20250415T110000Z
DTEND:20250415T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250415T090000Z
DTEND:20250415T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit definition\, continuity conditions\, diff
 erentiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima
DTSTART:20250415T110000Z
DTEND:20250415T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Mean value theorem
DTSTART:20250415T090000Z
DTEND:20250415T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMean value theorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Integration
DTSTART:20250415T110000Z
DTEND:20250415T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques\, definite and indefinit
 e integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables
DTSTART:20250415T090000Z
DTEND:20250415T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent types of random variables and their p
 roperties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Mean\, median\, mode\, standard
  deviation
DTSTART:20250415T110000Z
DTEND:20250415T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMeasures of central tendency and dispersion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250416T090000Z
DTEND:20250416T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical equivalence\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions
DTSTART:20250416T110000Z
DTEND:20250416T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Partial orders\, lattices
DTSTART:20250416T090000Z
DTEND:20250416T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPosets\, Hasse diagrams\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Monoids\, Groups
DTSTART:20250416T110000Z
DTEND:20250416T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, subgroups\, homomorphisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Graphs
DTSTART:20250416T090000Z
DTEND:20250416T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, connectivity\, matching
 \, coloring
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Combinatorics
DTSTART:20250416T110000Z
DTEND:20250416T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCounting principles\, recurrence relations\, ge
 nerating functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250416T090000Z
DTEND:20250416T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations
DTSTART:20250416T110000Z
DTEND:20250416T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inverse
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Eigenvalues and eigenvectors
DTSTART:20250416T090000Z
DTEND:20250416T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue computation\, eigenvectors propertie
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: LU decomposition
DTSTART:20250416T110000Z
DTEND:20250416T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250416T090000Z
DTEND:20250416T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit definition\, continuity conditions\, diff
 erentiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima
DTSTART:20250416T110000Z
DTEND:20250416T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Mean value theorem
DTSTART:20250416T090000Z
DTEND:20250416T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMean value theorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Integration
DTSTART:20250416T110000Z
DTEND:20250416T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques\, definite and indefinit
 e integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables
DTSTART:20250416T090000Z
DTEND:20250416T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent types of random variables and their p
 roperties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Mean\, median\, mode\, standard
  deviation
DTSTART:20250416T110000Z
DTEND:20250416T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMeasures of central tendency and dispersion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250417T090000Z
DTEND:20250417T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical equivalence\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions
DTSTART:20250417T110000Z
DTEND:20250417T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Partial orders\, lattices
DTSTART:20250417T090000Z
DTEND:20250417T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPosets\, Hasse diagrams\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Monoids\, Groups
DTSTART:20250417T110000Z
DTEND:20250417T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, subgroups\, homomorphisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Graphs
DTSTART:20250417T090000Z
DTEND:20250417T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, connectivity\, matching
 \, coloring
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Combinatorics
DTSTART:20250417T110000Z
DTEND:20250417T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCounting principles\, recurrence relations\, ge
 nerating functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250417T090000Z
DTEND:20250417T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations
DTSTART:20250417T110000Z
DTEND:20250417T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inverse
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Eigenvalues and eigenvectors
DTSTART:20250417T090000Z
DTEND:20250417T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue computation\, eigenvectors propertie
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: LU decomposition
DTSTART:20250417T110000Z
DTEND:20250417T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250417T090000Z
DTEND:20250417T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit definition\, continuity conditions\, diff
 erentiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima
DTSTART:20250417T110000Z
DTEND:20250417T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Mean value theorem
DTSTART:20250417T090000Z
DTEND:20250417T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMean value theorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Integration
DTSTART:20250417T110000Z
DTEND:20250417T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques\, definite and indefinit
 e integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables
DTSTART:20250417T090000Z
DTEND:20250417T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent types of random variables and their p
 roperties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Mean\, median\, mode\, standard
  deviation
DTSTART:20250417T110000Z
DTEND:20250417T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMeasures of central tendency and dispersion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250418T090000Z
DTEND:20250418T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical equivalence\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions
DTSTART:20250418T110000Z
DTEND:20250418T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Partial orders\, lattices
DTSTART:20250418T090000Z
DTEND:20250418T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPosets\, Hasse diagrams\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Monoids\, Groups
DTSTART:20250418T110000Z
DTEND:20250418T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, subgroups\, homomorphisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Graphs
DTSTART:20250418T090000Z
DTEND:20250418T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, connectivity\, matching
 \, coloring
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Combinatorics
DTSTART:20250418T110000Z
DTEND:20250418T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCounting principles\, recurrence relations\, ge
 nerating functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250418T090000Z
DTEND:20250418T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations
DTSTART:20250418T110000Z
DTEND:20250418T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inverse
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Eigenvalues and eigenvectors
DTSTART:20250418T090000Z
DTEND:20250418T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue computation\, eigenvectors propertie
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: LU decomposition
DTSTART:20250418T110000Z
DTEND:20250418T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250418T090000Z
DTEND:20250418T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit definition\, continuity conditions\, diff
 erentiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima
DTSTART:20250418T110000Z
DTEND:20250418T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Mean value theorem
DTSTART:20250418T090000Z
DTEND:20250418T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMean value theorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Integration
DTSTART:20250418T110000Z
DTEND:20250418T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques\, definite and indefinit
 e integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables
DTSTART:20250418T090000Z
DTEND:20250418T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent types of random variables and their p
 roperties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Mean\, median\, mode\, standard
  deviation
DTSTART:20250418T110000Z
DTEND:20250418T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMeasures of central tendency and dispersion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250419T090000Z
DTEND:20250419T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical equivalence\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions
DTSTART:20250419T110000Z
DTEND:20250419T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Partial orders\, lattices
DTSTART:20250419T090000Z
DTEND:20250419T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPosets\, Hasse diagrams\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Monoids\, Groups
DTSTART:20250419T110000Z
DTEND:20250419T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, subgroups\, homomorphisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Graphs
DTSTART:20250419T090000Z
DTEND:20250419T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, connectivity\, matching
 \, coloring
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Combinatorics
DTSTART:20250419T110000Z
DTEND:20250419T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCounting principles\, recurrence relations\, ge
 nerating functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250419T090000Z
DTEND:20250419T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations
DTSTART:20250419T110000Z
DTEND:20250419T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inverse
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Eigenvalues and eigenvectors
DTSTART:20250419T090000Z
DTEND:20250419T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue computation\, eigenvectors propertie
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: LU decomposition
DTSTART:20250419T110000Z
DTEND:20250419T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250419T090000Z
DTEND:20250419T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit definition\, continuity conditions\, diff
 erentiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima
DTSTART:20250419T110000Z
DTEND:20250419T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Mean value theorem
DTSTART:20250419T090000Z
DTEND:20250419T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMean value theorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Integration
DTSTART:20250419T110000Z
DTEND:20250419T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques\, definite and indefinit
 e integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables
DTSTART:20250419T090000Z
DTEND:20250419T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent types of random variables and their p
 roperties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Mean\, median\, mode\, standard
  deviation
DTSTART:20250419T110000Z
DTEND:20250419T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMeasures of central tendency and dispersion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Propositional and first-order logic
DTSTART:20250420T090000Z
DTEND:20250420T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, quantifiers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Sets\, relations\, functions
DTSTART:20250420T110000Z
DTEND:20250420T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of relations\, function composition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Partial orders\, lattices
DTSTART:20250420T090000Z
DTEND:20250420T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPoset\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Monoids\, Groups
DTSTART:20250420T110000Z
DTEND:20250420T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, cyclic groups
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Graphs: Connectivity\, matching\, coloring
DTSTART:20250420T090000Z
DTEND:20250420T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, matching algorithms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Counting
DTSTART:20250420T110000Z
DTEND:20250420T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPermutation\, combination formulas
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Recurrence relations\, generating functions
DTSTART:20250420T090000Z
DTEND:20250420T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSolving linear recurrences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Matrices\, determinants
DTSTART:20250420T110000Z
DTEND:20250420T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of determinants
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: System of linear equations
DTSTART:20250420T090000Z
DTEND:20250420T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inversion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Eigenvalues and eigenvectors
DTSTART:20250420T110000Z
DTEND:20250420T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCharacteristic equation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: LU decomposition
DTSTART:20250420T090000Z
DTEND:20250420T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition algorithm
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Limits\, continuity\, and differentiability
DTSTART:20250420T110000Z
DTEND:20250420T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDefinition of limit\, continuity\, and derivati
 ve
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Maxima and minima
DTSTART:20250420T090000Z
DTEND:20250420T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Mean value theorem
DTSTART:20250420T110000Z
DTEND:20250420T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRolle's theorem
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Integration
DTSTART:20250420T090000Z
DTEND:20250420T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Random variables: Uniform\, normal distributions
DTSTART:20250420T110000Z
DTEND:20250420T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability density function\, cumulative distr
 ibution function
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Propositional and first-order logic
DTSTART:20250421T090000Z
DTEND:20250421T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, quantifiers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Sets\, relations\, functions
DTSTART:20250421T110000Z
DTEND:20250421T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of relations\, function composition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Partial orders\, lattices
DTSTART:20250421T090000Z
DTEND:20250421T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPoset\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Monoids\, Groups
DTSTART:20250421T110000Z
DTEND:20250421T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, cyclic groups
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Graphs: Connectivity\, matching\, coloring
DTSTART:20250421T090000Z
DTEND:20250421T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, matching algorithms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Counting
DTSTART:20250421T110000Z
DTEND:20250421T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPermutation\, combination formulas
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Recurrence relations\, generating functions
DTSTART:20250421T090000Z
DTEND:20250421T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSolving linear recurrences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Matrices\, determinants
DTSTART:20250421T110000Z
DTEND:20250421T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of determinants
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: System of linear equations
DTSTART:20250421T090000Z
DTEND:20250421T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inversion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Eigenvalues and eigenvectors
DTSTART:20250421T110000Z
DTEND:20250421T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCharacteristic equation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: LU decomposition
DTSTART:20250421T090000Z
DTEND:20250421T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition algorithm
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Limits\, continuity\, and differentiability
DTSTART:20250421T110000Z
DTEND:20250421T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDefinition of limit\, continuity\, and derivati
 ve
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Maxima and minima
DTSTART:20250421T090000Z
DTEND:20250421T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Mean value theorem
DTSTART:20250421T110000Z
DTEND:20250421T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRolle's theorem
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Integration
DTSTART:20250421T090000Z
DTEND:20250421T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Random variables: Uniform\, normal distributions
DTSTART:20250421T110000Z
DTEND:20250421T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability density function\, cumulative distr
 ibution function
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Propositional and first-order logic
DTSTART:20250422T090000Z
DTEND:20250422T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, quantifiers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Sets\, relations\, functions
DTSTART:20250422T110000Z
DTEND:20250422T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of relations\, function composition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Partial orders\, lattices
DTSTART:20250422T090000Z
DTEND:20250422T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPoset\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Monoids\, Groups
DTSTART:20250422T110000Z
DTEND:20250422T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, cyclic groups
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Graphs: Connectivity\, matching\, coloring
DTSTART:20250422T090000Z
DTEND:20250422T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, matching algorithms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Counting
DTSTART:20250422T110000Z
DTEND:20250422T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPermutation\, combination formulas
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Recurrence relations\, generating functions
DTSTART:20250422T090000Z
DTEND:20250422T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSolving linear recurrences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Matrices\, determinants
DTSTART:20250422T110000Z
DTEND:20250422T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of determinants
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: System of linear equations
DTSTART:20250422T090000Z
DTEND:20250422T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inversion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Eigenvalues and eigenvectors
DTSTART:20250422T110000Z
DTEND:20250422T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCharacteristic equation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: LU decomposition
DTSTART:20250422T090000Z
DTEND:20250422T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition algorithm
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Limits\, continuity\, and differentiability
DTSTART:20250422T110000Z
DTEND:20250422T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDefinition of limit\, continuity\, and derivati
 ve
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Maxima and minima
DTSTART:20250422T090000Z
DTEND:20250422T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Mean value theorem
DTSTART:20250422T110000Z
DTEND:20250422T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRolle's theorem
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Integration
DTSTART:20250422T090000Z
DTEND:20250422T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Random variables: Uniform\, normal distributions
DTSTART:20250422T110000Z
DTEND:20250422T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability density function\, cumulative distr
 ibution function
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Propositional and first-order logic
DTSTART:20250423T090000Z
DTEND:20250423T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, quantifiers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Sets\, relations\, functions
DTSTART:20250423T110000Z
DTEND:20250423T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of relations\, function composition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Partial orders\, lattices
DTSTART:20250423T090000Z
DTEND:20250423T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPoset\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Monoids\, Groups
DTSTART:20250423T110000Z
DTEND:20250423T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, cyclic groups
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Graphs: Connectivity\, matching\, coloring
DTSTART:20250423T090000Z
DTEND:20250423T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, matching algorithms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Counting
DTSTART:20250423T110000Z
DTEND:20250423T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPermutation\, combination formulas
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Recurrence relations\, generating functions
DTSTART:20250423T090000Z
DTEND:20250423T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSolving linear recurrences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Matrices\, determinants
DTSTART:20250423T110000Z
DTEND:20250423T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of determinants
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: System of linear equations
DTSTART:20250423T090000Z
DTEND:20250423T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inversion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Eigenvalues and eigenvectors
DTSTART:20250423T110000Z
DTEND:20250423T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCharacteristic equation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: LU decomposition
DTSTART:20250423T090000Z
DTEND:20250423T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition algorithm
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Limits\, continuity\, and differentiability
DTSTART:20250423T110000Z
DTEND:20250423T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDefinition of limit\, continuity\, and derivati
 ve
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Maxima and minima
DTSTART:20250423T090000Z
DTEND:20250423T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Mean value theorem
DTSTART:20250423T110000Z
DTEND:20250423T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRolle's theorem
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Integration
DTSTART:20250423T090000Z
DTEND:20250423T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Random variables: Uniform\, normal distributions
DTSTART:20250423T110000Z
DTEND:20250423T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability density function\, cumulative distr
 ibution function
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Propositional and first-order logic
DTSTART:20250424T090000Z
DTEND:20250424T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, quantifiers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Sets\, relations\, functions
DTSTART:20250424T110000Z
DTEND:20250424T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of relations\, function composition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Partial orders\, lattices
DTSTART:20250424T090000Z
DTEND:20250424T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPoset\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Monoids\, Groups
DTSTART:20250424T110000Z
DTEND:20250424T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, cyclic groups
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Graphs: Connectivity\, matching\, coloring
DTSTART:20250424T090000Z
DTEND:20250424T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, matching algorithms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Counting
DTSTART:20250424T110000Z
DTEND:20250424T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPermutation\, combination formulas
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Recurrence relations\, generating functions
DTSTART:20250424T090000Z
DTEND:20250424T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSolving linear recurrences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Matrices\, determinants
DTSTART:20250424T110000Z
DTEND:20250424T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of determinants
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: System of linear equations
DTSTART:20250424T090000Z
DTEND:20250424T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inversion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Eigenvalues and eigenvectors
DTSTART:20250424T110000Z
DTEND:20250424T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCharacteristic equation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: LU decomposition
DTSTART:20250424T090000Z
DTEND:20250424T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition algorithm
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Limits\, continuity\, and differentiability
DTSTART:20250424T110000Z
DTEND:20250424T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDefinition of limit\, continuity\, and derivati
 ve
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Maxima and minima
DTSTART:20250424T090000Z
DTEND:20250424T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Mean value theorem
DTSTART:20250424T110000Z
DTEND:20250424T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRolle's theorem
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Integration
DTSTART:20250424T090000Z
DTEND:20250424T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Random variables: Uniform\, normal distributions
DTSTART:20250424T110000Z
DTEND:20250424T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability density function\, cumulative distr
 ibution function
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Propositional and first-order logic
DTSTART:20250425T090000Z
DTEND:20250425T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, quantifiers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Sets\, relations\, functions
DTSTART:20250425T110000Z
DTEND:20250425T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of relations\, function composition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Partial orders\, lattices
DTSTART:20250425T090000Z
DTEND:20250425T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPoset\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Monoids\, Groups
DTSTART:20250425T110000Z
DTEND:20250425T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, cyclic groups
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Graphs: Connectivity\, matching\, coloring
DTSTART:20250425T090000Z
DTEND:20250425T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, matching algorithms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Counting
DTSTART:20250425T110000Z
DTEND:20250425T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPermutation\, combination formulas
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Recurrence relations\, generating functions
DTSTART:20250425T090000Z
DTEND:20250425T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSolving linear recurrences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Matrices\, determinants
DTSTART:20250425T110000Z
DTEND:20250425T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of determinants
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: System of linear equations
DTSTART:20250425T090000Z
DTEND:20250425T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inversion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Eigenvalues and eigenvectors
DTSTART:20250425T110000Z
DTEND:20250425T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCharacteristic equation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: LU decomposition
DTSTART:20250425T090000Z
DTEND:20250425T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition algorithm
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Limits\, continuity\, and differentiability
DTSTART:20250425T110000Z
DTEND:20250425T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDefinition of limit\, continuity\, and derivati
 ve
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Maxima and minima
DTSTART:20250425T090000Z
DTEND:20250425T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Mean value theorem
DTSTART:20250425T110000Z
DTEND:20250425T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRolle's theorem
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Integration
DTSTART:20250425T090000Z
DTEND:20250425T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Random variables: Uniform\, normal distributions
DTSTART:20250425T110000Z
DTEND:20250425T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability density function\, cumulative distr
 ibution function
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Propositional and first-order logic
DTSTART:20250426T090000Z
DTEND:20250426T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, quantifiers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Sets\, relations\, functions
DTSTART:20250426T110000Z
DTEND:20250426T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of relations\, function composition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Partial orders\, lattices
DTSTART:20250426T090000Z
DTEND:20250426T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPoset\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Monoids\, Groups
DTSTART:20250426T110000Z
DTEND:20250426T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, cyclic groups
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Graphs: Connectivity\, matching\, coloring
DTSTART:20250426T090000Z
DTEND:20250426T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, matching algorithms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Counting
DTSTART:20250426T110000Z
DTEND:20250426T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPermutation\, combination formulas
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Recurrence relations\, generating functions
DTSTART:20250426T090000Z
DTEND:20250426T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSolving linear recurrences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Matrices\, determinants
DTSTART:20250426T110000Z
DTEND:20250426T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of determinants
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: System of linear equations
DTSTART:20250426T090000Z
DTEND:20250426T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inversion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Eigenvalues and eigenvectors
DTSTART:20250426T110000Z
DTEND:20250426T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCharacteristic equation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: LU decomposition
DTSTART:20250426T090000Z
DTEND:20250426T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition algorithm
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Limits\, continuity\, and differentiability
DTSTART:20250426T110000Z
DTEND:20250426T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDefinition of limit\, continuity\, and derivati
 ve
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Maxima and minima
DTSTART:20250426T090000Z
DTEND:20250426T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Mean value theorem
DTSTART:20250426T110000Z
DTEND:20250426T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRolle's theorem
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Integration
DTSTART:20250426T090000Z
DTEND:20250426T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Random variables: Uniform\, normal distributions
DTSTART:20250426T110000Z
DTEND:20250426T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability density function\, cumulative distr
 ibution function
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Propositional and first-order logic
DTSTART:20250427T090000Z
DTEND:20250427T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, quantifiers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Sets\, relations\, functions
DTSTART:20250427T110000Z
DTEND:20250427T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of relations\, function composition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Partial orders\, lattices
DTSTART:20250427T090000Z
DTEND:20250427T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPoset\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Monoids\, Groups
DTSTART:20250427T110000Z
DTEND:20250427T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGroup axioms\, cyclic groups
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Graphs: Connectivity\, matching\, coloring
DTSTART:20250427T090000Z
DTEND:20250427T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGraph representations\, matching algorithms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Counting
DTSTART:20250427T110000Z
DTEND:20250427T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPermutation\, combination formulas
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Combinatorics: Recurrence relations\, generating functions
DTSTART:20250427T090000Z
DTEND:20250427T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSolving linear recurrences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Matrices\, determinants
DTSTART:20250427T110000Z
DTEND:20250427T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProperties of determinants
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: System of linear equations
DTSTART:20250427T090000Z
DTEND:20250427T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, matrix inversion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Eigenvalues and eigenvectors
DTSTART:20250427T110000Z
DTEND:20250427T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCharacteristic equation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: LU decomposition
DTSTART:20250427T090000Z
DTEND:20250427T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLU decomposition algorithm
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Limits\, continuity\, and differentiability
DTSTART:20250427T110000Z
DTEND:20250427T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDefinition of limit\, continuity\, and derivati
 ve
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Maxima and minima
DTSTART:20250427T090000Z
DTEND:20250427T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFirst and second derivative tests
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Mean value theorem
DTSTART:20250427T110000Z
DTEND:20250427T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRolle's theorem
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Integration
DTSTART:20250427T090000Z
DTEND:20250427T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntegration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Random variables: Uniform\, normal distributions
DTSTART:20250427T110000Z
DTEND:20250427T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability density function\, cumulative distr
 ibution function
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250428T090000Z
DTEND:20250428T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra simplification
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250428T110000Z
DTEND:20250428T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCombinational vs Sequential circuits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250428T090000Z
DTEND:20250428T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRecursion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250428T090000Z
DTEND:20250428T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAsymptotic notation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250428T090000Z
DTEND:20250428T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250428T090000Z
DTEND:20250428T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250428T090000Z
DTEND:20250428T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250428T090000Z
DTEND:20250428T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250428T090000Z
DTEND:20250428T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP models
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250429T090000Z
DTEND:20250429T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra simplification
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250429T110000Z
DTEND:20250429T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCombinational vs Sequential circuits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250429T090000Z
DTEND:20250429T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRecursion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250429T090000Z
DTEND:20250429T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAsymptotic notation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250429T090000Z
DTEND:20250429T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250429T090000Z
DTEND:20250429T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250429T090000Z
DTEND:20250429T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250429T090000Z
DTEND:20250429T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250429T090000Z
DTEND:20250429T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP models
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250430T090000Z
DTEND:20250430T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra simplification
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250430T110000Z
DTEND:20250430T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCombinational vs Sequential circuits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250430T090000Z
DTEND:20250430T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRecursion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250430T090000Z
DTEND:20250430T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAsymptotic notation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250430T090000Z
DTEND:20250430T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250430T090000Z
DTEND:20250430T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250430T090000Z
DTEND:20250430T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250430T090000Z
DTEND:20250430T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250430T090000Z
DTEND:20250430T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP models
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250501T090000Z
DTEND:20250501T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra simplification
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250501T110000Z
DTEND:20250501T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCombinational vs Sequential circuits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250501T090000Z
DTEND:20250501T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRecursion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250501T090000Z
DTEND:20250501T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAsymptotic notation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250501T090000Z
DTEND:20250501T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250501T090000Z
DTEND:20250501T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250501T090000Z
DTEND:20250501T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250501T090000Z
DTEND:20250501T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250501T090000Z
DTEND:20250501T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP models
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250502T090000Z
DTEND:20250502T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra simplification
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250502T110000Z
DTEND:20250502T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCombinational vs Sequential circuits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250502T090000Z
DTEND:20250502T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRecursion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250502T090000Z
DTEND:20250502T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAsymptotic notation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250502T090000Z
DTEND:20250502T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250502T090000Z
DTEND:20250502T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250502T090000Z
DTEND:20250502T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250502T090000Z
DTEND:20250502T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250502T090000Z
DTEND:20250502T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP models
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250503T090000Z
DTEND:20250503T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra simplification
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250503T110000Z
DTEND:20250503T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCombinational vs Sequential circuits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250503T090000Z
DTEND:20250503T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRecursion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250503T090000Z
DTEND:20250503T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAsymptotic notation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250503T090000Z
DTEND:20250503T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250503T090000Z
DTEND:20250503T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250503T090000Z
DTEND:20250503T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250503T090000Z
DTEND:20250503T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250503T090000Z
DTEND:20250503T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP models
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250504T090000Z
DTEND:20250504T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra simplification
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250504T110000Z
DTEND:20250504T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCombinational vs Sequential circuits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250504T090000Z
DTEND:20250504T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRecursion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250504T090000Z
DTEND:20250504T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAsymptotic notation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250504T090000Z
DTEND:20250504T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250504T090000Z
DTEND:20250504T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250504T090000Z
DTEND:20250504T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250504T090000Z
DTEND:20250504T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250504T090000Z
DTEND:20250504T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP models
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250505T090000Z
DTEND:20250505T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra simplification
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250505T110000Z
DTEND:20250505T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCombinational vs Sequential circuits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250505T090000Z
DTEND:20250505T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRecursion
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250505T090000Z
DTEND:20250505T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAsymptotic notation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250505T090000Z
DTEND:20250505T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250505T090000Z
DTEND:20250505T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250505T090000Z
DTEND:20250505T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250505T090000Z
DTEND:20250505T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250505T090000Z
DTEND:20250505T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP models
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250506T090000Z
DTEND:20250506T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250506T110000Z
DTEND:20250506T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250506T090000Z
DTEND:20250506T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250506T110000Z
DTEND:20250506T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250506T090000Z
DTEND:20250506T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250506T110000Z
DTEND:20250506T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSemantic analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250506T090000Z
DTEND:20250506T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250506T110000Z
DTEND:20250506T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250506T090000Z
DTEND:20250506T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250506T110000Z
DTEND:20250506T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nQuadruples
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250506T090000Z
DTEND:20250506T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250506T110000Z
DTEND:20250506T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDead code elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250506T090000Z
DTEND:20250506T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250506T110000Z
DTEND:20250506T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLiveness analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250506T090000Z
DTEND:20250506T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250506T110000Z
DTEND:20250506T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelationship
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250507T090000Z
DTEND:20250507T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250507T110000Z
DTEND:20250507T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250507T090000Z
DTEND:20250507T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250507T110000Z
DTEND:20250507T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250507T090000Z
DTEND:20250507T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250507T110000Z
DTEND:20250507T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSemantic analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250507T090000Z
DTEND:20250507T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250507T110000Z
DTEND:20250507T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250507T090000Z
DTEND:20250507T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250507T110000Z
DTEND:20250507T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nQuadruples
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250507T090000Z
DTEND:20250507T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250507T110000Z
DTEND:20250507T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDead code elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250507T090000Z
DTEND:20250507T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250507T110000Z
DTEND:20250507T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLiveness analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250507T090000Z
DTEND:20250507T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250507T110000Z
DTEND:20250507T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelationship
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250508T090000Z
DTEND:20250508T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250508T110000Z
DTEND:20250508T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250508T090000Z
DTEND:20250508T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250508T110000Z
DTEND:20250508T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250508T090000Z
DTEND:20250508T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250508T110000Z
DTEND:20250508T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSemantic analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250508T090000Z
DTEND:20250508T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250508T110000Z
DTEND:20250508T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250508T090000Z
DTEND:20250508T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250508T110000Z
DTEND:20250508T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nQuadruples
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250508T090000Z
DTEND:20250508T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250508T110000Z
DTEND:20250508T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDead code elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250508T090000Z
DTEND:20250508T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250508T110000Z
DTEND:20250508T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLiveness analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250508T090000Z
DTEND:20250508T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250508T110000Z
DTEND:20250508T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelationship
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250509T090000Z
DTEND:20250509T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250509T110000Z
DTEND:20250509T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250509T090000Z
DTEND:20250509T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250509T110000Z
DTEND:20250509T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250509T090000Z
DTEND:20250509T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250509T110000Z
DTEND:20250509T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSemantic analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250509T090000Z
DTEND:20250509T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250509T110000Z
DTEND:20250509T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250509T090000Z
DTEND:20250509T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250509T110000Z
DTEND:20250509T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nQuadruples
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250509T090000Z
DTEND:20250509T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250509T110000Z
DTEND:20250509T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDead code elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250509T090000Z
DTEND:20250509T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250509T110000Z
DTEND:20250509T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLiveness analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250509T090000Z
DTEND:20250509T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250509T110000Z
DTEND:20250509T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelationship
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250510T090000Z
DTEND:20250510T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250510T110000Z
DTEND:20250510T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250510T090000Z
DTEND:20250510T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250510T110000Z
DTEND:20250510T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250510T090000Z
DTEND:20250510T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250510T110000Z
DTEND:20250510T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSemantic analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250510T090000Z
DTEND:20250510T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250510T110000Z
DTEND:20250510T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250510T090000Z
DTEND:20250510T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250510T110000Z
DTEND:20250510T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nQuadruples
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250510T090000Z
DTEND:20250510T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250510T110000Z
DTEND:20250510T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDead code elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250510T090000Z
DTEND:20250510T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250510T110000Z
DTEND:20250510T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLiveness analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250510T090000Z
DTEND:20250510T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250510T110000Z
DTEND:20250510T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelationship
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250511T090000Z
DTEND:20250511T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250511T110000Z
DTEND:20250511T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250511T090000Z
DTEND:20250511T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250511T110000Z
DTEND:20250511T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250511T090000Z
DTEND:20250511T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250511T110000Z
DTEND:20250511T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSemantic analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250511T090000Z
DTEND:20250511T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250511T110000Z
DTEND:20250511T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250511T090000Z
DTEND:20250511T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250511T110000Z
DTEND:20250511T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nQuadruples
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250511T090000Z
DTEND:20250511T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250511T110000Z
DTEND:20250511T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDead code elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250511T090000Z
DTEND:20250511T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250511T110000Z
DTEND:20250511T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLiveness analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250511T090000Z
DTEND:20250511T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250511T110000Z
DTEND:20250511T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelationship
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250512T090000Z
DTEND:20250512T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250512T110000Z
DTEND:20250512T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250512T090000Z
DTEND:20250512T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250512T110000Z
DTEND:20250512T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250512T090000Z
DTEND:20250512T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250512T110000Z
DTEND:20250512T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSemantic analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250512T090000Z
DTEND:20250512T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250512T110000Z
DTEND:20250512T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250512T090000Z
DTEND:20250512T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250512T110000Z
DTEND:20250512T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nQuadruples
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250512T090000Z
DTEND:20250512T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250512T110000Z
DTEND:20250512T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDead code elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250512T090000Z
DTEND:20250512T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250512T110000Z
DTEND:20250512T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLiveness analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250512T090000Z
DTEND:20250512T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250512T110000Z
DTEND:20250512T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelationship
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250513T090000Z
DTEND:20250513T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250513T110000Z
DTEND:20250513T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250513T090000Z
DTEND:20250513T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250513T110000Z
DTEND:20250513T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250513T090000Z
DTEND:20250513T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250513T110000Z
DTEND:20250513T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSemantic analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250513T090000Z
DTEND:20250513T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250513T110000Z
DTEND:20250513T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250513T090000Z
DTEND:20250513T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250513T110000Z
DTEND:20250513T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nQuadruples
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250513T090000Z
DTEND:20250513T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250513T110000Z
DTEND:20250513T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDead code elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250513T090000Z
DTEND:20250513T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250513T110000Z
DTEND:20250513T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLiveness analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250513T090000Z
DTEND:20250513T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250513T110000Z
DTEND:20250513T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelationship
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250514T090000Z
DTEND:20250514T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls
DTSTART:20250514T110000Z
DTEND:20250514T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250514T090000Z
DTEND:20250514T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Processes and Threads
DTSTART:20250514T110000Z
DTEND:20250514T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250514T090000Z
DTEND:20250514T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Inter-process Communication
DTSTART:20250514T110000Z
DTEND:20250514T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250514T090000Z
DTEND:20250514T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250514T110000Z
DTEND:20250514T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nStack frames
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250514T090000Z
DTEND:20250514T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250514T110000Z
DTEND:20250514T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttributes
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints
DTSTART:20250514T090000Z
DTEND:20250514T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPrimary keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Normal Forms
DTSTART:20250514T110000Z
DTEND:20250514T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nForeign keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250514T090000Z
DTEND:20250514T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250514T110000Z
DTEND:20250514T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP/IP model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250514T090000Z
DTEND:20250514T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDistance vector routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: IP Addressing
DTSTART:20250514T110000Z
DTEND:20250514T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLink-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250515T090000Z
DTEND:20250515T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls
DTSTART:20250515T110000Z
DTEND:20250515T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250515T090000Z
DTEND:20250515T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Processes and Threads
DTSTART:20250515T110000Z
DTEND:20250515T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250515T090000Z
DTEND:20250515T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Inter-process Communication
DTSTART:20250515T110000Z
DTEND:20250515T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250515T090000Z
DTEND:20250515T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250515T110000Z
DTEND:20250515T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nStack frames
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250515T090000Z
DTEND:20250515T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250515T110000Z
DTEND:20250515T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttributes
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints
DTSTART:20250515T090000Z
DTEND:20250515T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPrimary keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Normal Forms
DTSTART:20250515T110000Z
DTEND:20250515T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nForeign keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250515T090000Z
DTEND:20250515T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250515T110000Z
DTEND:20250515T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP/IP model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250515T090000Z
DTEND:20250515T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDistance vector routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: IP Addressing
DTSTART:20250515T110000Z
DTEND:20250515T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLink-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250516T090000Z
DTEND:20250516T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls
DTSTART:20250516T110000Z
DTEND:20250516T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250516T090000Z
DTEND:20250516T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Processes and Threads
DTSTART:20250516T110000Z
DTEND:20250516T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250516T090000Z
DTEND:20250516T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Inter-process Communication
DTSTART:20250516T110000Z
DTEND:20250516T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250516T090000Z
DTEND:20250516T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250516T110000Z
DTEND:20250516T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nStack frames
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250516T090000Z
DTEND:20250516T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250516T110000Z
DTEND:20250516T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttributes
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints
DTSTART:20250516T090000Z
DTEND:20250516T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPrimary keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Normal Forms
DTSTART:20250516T110000Z
DTEND:20250516T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nForeign keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250516T090000Z
DTEND:20250516T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250516T110000Z
DTEND:20250516T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP/IP model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250516T090000Z
DTEND:20250516T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDistance vector routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: IP Addressing
DTSTART:20250516T110000Z
DTEND:20250516T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLink-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250517T090000Z
DTEND:20250517T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls
DTSTART:20250517T110000Z
DTEND:20250517T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250517T090000Z
DTEND:20250517T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Processes and Threads
DTSTART:20250517T110000Z
DTEND:20250517T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250517T090000Z
DTEND:20250517T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Inter-process Communication
DTSTART:20250517T110000Z
DTEND:20250517T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250517T090000Z
DTEND:20250517T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250517T110000Z
DTEND:20250517T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nStack frames
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250517T090000Z
DTEND:20250517T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250517T110000Z
DTEND:20250517T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttributes
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints
DTSTART:20250517T090000Z
DTEND:20250517T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPrimary keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Normal Forms
DTSTART:20250517T110000Z
DTEND:20250517T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nForeign keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250517T090000Z
DTEND:20250517T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250517T110000Z
DTEND:20250517T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP/IP model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250517T090000Z
DTEND:20250517T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDistance vector routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: IP Addressing
DTSTART:20250517T110000Z
DTEND:20250517T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLink-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250518T090000Z
DTEND:20250518T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls
DTSTART:20250518T110000Z
DTEND:20250518T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250518T090000Z
DTEND:20250518T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Processes and Threads
DTSTART:20250518T110000Z
DTEND:20250518T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250518T090000Z
DTEND:20250518T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Inter-process Communication
DTSTART:20250518T110000Z
DTEND:20250518T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250518T090000Z
DTEND:20250518T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250518T110000Z
DTEND:20250518T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nStack frames
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250518T090000Z
DTEND:20250518T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250518T110000Z
DTEND:20250518T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttributes
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints
DTSTART:20250518T090000Z
DTEND:20250518T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPrimary keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Normal Forms
DTSTART:20250518T110000Z
DTEND:20250518T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nForeign keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250518T090000Z
DTEND:20250518T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250518T110000Z
DTEND:20250518T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP/IP model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250518T090000Z
DTEND:20250518T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDistance vector routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: IP Addressing
DTSTART:20250518T110000Z
DTEND:20250518T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLink-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250519T090000Z
DTEND:20250519T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls
DTSTART:20250519T110000Z
DTEND:20250519T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250519T090000Z
DTEND:20250519T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Processes and Threads
DTSTART:20250519T110000Z
DTEND:20250519T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250519T090000Z
DTEND:20250519T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Inter-process Communication
DTSTART:20250519T110000Z
DTEND:20250519T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250519T090000Z
DTEND:20250519T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250519T110000Z
DTEND:20250519T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nStack frames
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250519T090000Z
DTEND:20250519T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250519T110000Z
DTEND:20250519T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttributes
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints
DTSTART:20250519T090000Z
DTEND:20250519T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPrimary keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Normal Forms
DTSTART:20250519T110000Z
DTEND:20250519T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nForeign keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250519T090000Z
DTEND:20250519T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250519T110000Z
DTEND:20250519T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP/IP model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250519T090000Z
DTEND:20250519T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDistance vector routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: IP Addressing
DTSTART:20250519T110000Z
DTEND:20250519T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLink-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250520T090000Z
DTEND:20250520T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls
DTSTART:20250520T110000Z
DTEND:20250520T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250520T090000Z
DTEND:20250520T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Processes and Threads
DTSTART:20250520T110000Z
DTEND:20250520T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250520T090000Z
DTEND:20250520T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Inter-process Communication
DTSTART:20250520T110000Z
DTEND:20250520T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250520T090000Z
DTEND:20250520T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250520T110000Z
DTEND:20250520T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nStack frames
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250520T090000Z
DTEND:20250520T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250520T110000Z
DTEND:20250520T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttributes
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints
DTSTART:20250520T090000Z
DTEND:20250520T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPrimary keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Normal Forms
DTSTART:20250520T110000Z
DTEND:20250520T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nForeign keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250520T090000Z
DTEND:20250520T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250520T110000Z
DTEND:20250520T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP/IP model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250520T090000Z
DTEND:20250520T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDistance vector routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: IP Addressing
DTSTART:20250520T110000Z
DTEND:20250520T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLink-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250521T090000Z
DTEND:20250521T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls
DTSTART:20250521T110000Z
DTEND:20250521T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250521T090000Z
DTEND:20250521T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Processes and Threads
DTSTART:20250521T110000Z
DTEND:20250521T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250521T090000Z
DTEND:20250521T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Inter-process Communication
DTSTART:20250521T110000Z
DTEND:20250521T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250521T090000Z
DTEND:20250521T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250521T110000Z
DTEND:20250521T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nStack frames
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250521T090000Z
DTEND:20250521T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250521T110000Z
DTEND:20250521T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttributes
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints
DTSTART:20250521T090000Z
DTEND:20250521T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPrimary keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Normal Forms
DTSTART:20250521T110000Z
DTEND:20250521T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nForeign keys
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250521T090000Z
DTEND:20250521T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250521T110000Z
DTEND:20250521T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP/IP model
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250521T090000Z
DTEND:20250521T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDistance vector routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: IP Addressing
DTSTART:20250521T110000Z
DTEND:20250521T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLink-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250522T090000Z
DTEND:20250522T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analyzer design
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250522T110000Z
DTEND:20250522T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250522T090000Z
DTEND:20250522T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParsing techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250522T110000Z
DTEND:20250522T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250522T090000Z
DTEND:20250522T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250522T110000Z
DTEND:20250522T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250522T090000Z
DTEND:20250522T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250522T110000Z
DTEND:20250522T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation & Optimizatio
 n
DTSTART:20250522T090000Z
DTEND:20250522T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250522T110000Z
DTEND:20250522T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCode optimization techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250522T090000Z
DTEND:20250522T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250522T110000Z
DTEND:20250522T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250522T090000Z
DTEND:20250522T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250522T110000Z
DTEND:20250522T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250522T090000Z
DTEND:20250522T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: File Systems
DTSTART:20250522T110000Z
DTEND:20250522T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nHashing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250523T090000Z
DTEND:20250523T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analyzer design
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250523T110000Z
DTEND:20250523T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250523T090000Z
DTEND:20250523T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParsing techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250523T110000Z
DTEND:20250523T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250523T090000Z
DTEND:20250523T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250523T110000Z
DTEND:20250523T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250523T090000Z
DTEND:20250523T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250523T110000Z
DTEND:20250523T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation & Optimizatio
 n
DTSTART:20250523T090000Z
DTEND:20250523T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250523T110000Z
DTEND:20250523T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCode optimization techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250523T090000Z
DTEND:20250523T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250523T110000Z
DTEND:20250523T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250523T090000Z
DTEND:20250523T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250523T110000Z
DTEND:20250523T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250523T090000Z
DTEND:20250523T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: File Systems
DTSTART:20250523T110000Z
DTEND:20250523T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nHashing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250524T090000Z
DTEND:20250524T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analyzer design
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250524T110000Z
DTEND:20250524T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250524T090000Z
DTEND:20250524T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParsing techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250524T110000Z
DTEND:20250524T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250524T090000Z
DTEND:20250524T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250524T110000Z
DTEND:20250524T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250524T090000Z
DTEND:20250524T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250524T110000Z
DTEND:20250524T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation & Optimizatio
 n
DTSTART:20250524T090000Z
DTEND:20250524T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250524T110000Z
DTEND:20250524T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCode optimization techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250524T090000Z
DTEND:20250524T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250524T110000Z
DTEND:20250524T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250524T090000Z
DTEND:20250524T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250524T110000Z
DTEND:20250524T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250524T090000Z
DTEND:20250524T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: File Systems
DTSTART:20250524T110000Z
DTEND:20250524T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nHashing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250525T090000Z
DTEND:20250525T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analyzer design
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250525T110000Z
DTEND:20250525T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250525T090000Z
DTEND:20250525T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParsing techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250525T110000Z
DTEND:20250525T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250525T090000Z
DTEND:20250525T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250525T110000Z
DTEND:20250525T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250525T090000Z
DTEND:20250525T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250525T110000Z
DTEND:20250525T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation & Optimizatio
 n
DTSTART:20250525T090000Z
DTEND:20250525T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250525T110000Z
DTEND:20250525T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCode optimization techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250525T090000Z
DTEND:20250525T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250525T110000Z
DTEND:20250525T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250525T090000Z
DTEND:20250525T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250525T110000Z
DTEND:20250525T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250525T090000Z
DTEND:20250525T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: File Systems
DTSTART:20250525T110000Z
DTEND:20250525T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nHashing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250526T090000Z
DTEND:20250526T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analyzer design
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250526T110000Z
DTEND:20250526T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250526T090000Z
DTEND:20250526T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParsing techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250526T110000Z
DTEND:20250526T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250526T090000Z
DTEND:20250526T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250526T110000Z
DTEND:20250526T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250526T090000Z
DTEND:20250526T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250526T110000Z
DTEND:20250526T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation & Optimizatio
 n
DTSTART:20250526T090000Z
DTEND:20250526T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250526T110000Z
DTEND:20250526T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCode optimization techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250526T090000Z
DTEND:20250526T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250526T110000Z
DTEND:20250526T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250526T090000Z
DTEND:20250526T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250526T110000Z
DTEND:20250526T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250526T090000Z
DTEND:20250526T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: File Systems
DTSTART:20250526T110000Z
DTEND:20250526T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nHashing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250527T090000Z
DTEND:20250527T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analyzer design
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250527T110000Z
DTEND:20250527T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250527T090000Z
DTEND:20250527T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParsing techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250527T110000Z
DTEND:20250527T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250527T090000Z
DTEND:20250527T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250527T110000Z
DTEND:20250527T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250527T090000Z
DTEND:20250527T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250527T110000Z
DTEND:20250527T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation & Optimizatio
 n
DTSTART:20250527T090000Z
DTEND:20250527T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250527T110000Z
DTEND:20250527T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCode optimization techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250527T090000Z
DTEND:20250527T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250527T110000Z
DTEND:20250527T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250527T090000Z
DTEND:20250527T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250527T110000Z
DTEND:20250527T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250527T090000Z
DTEND:20250527T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: File Systems
DTSTART:20250527T110000Z
DTEND:20250527T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nHashing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250528T090000Z
DTEND:20250528T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analyzer design
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250528T110000Z
DTEND:20250528T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250528T090000Z
DTEND:20250528T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParsing techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250528T110000Z
DTEND:20250528T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250528T090000Z
DTEND:20250528T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250528T110000Z
DTEND:20250528T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250528T090000Z
DTEND:20250528T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250528T110000Z
DTEND:20250528T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation & Optimizatio
 n
DTSTART:20250528T090000Z
DTEND:20250528T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250528T110000Z
DTEND:20250528T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCode optimization techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250528T090000Z
DTEND:20250528T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250528T110000Z
DTEND:20250528T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250528T090000Z
DTEND:20250528T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250528T110000Z
DTEND:20250528T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250528T090000Z
DTEND:20250528T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: File Systems
DTSTART:20250528T110000Z
DTEND:20250528T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nHashing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250529T090000Z
DTEND:20250529T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analyzer design
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250529T110000Z
DTEND:20250529T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250529T090000Z
DTEND:20250529T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParsing techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250529T110000Z
DTEND:20250529T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250529T090000Z
DTEND:20250529T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250529T110000Z
DTEND:20250529T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250529T090000Z
DTEND:20250529T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250529T110000Z
DTEND:20250529T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation & Optimizatio
 n
DTSTART:20250529T090000Z
DTEND:20250529T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250529T110000Z
DTEND:20250529T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCode optimization techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250529T090000Z
DTEND:20250529T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250529T110000Z
DTEND:20250529T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250529T090000Z
DTEND:20250529T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250529T110000Z
DTEND:20250529T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250529T090000Z
DTEND:20250529T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: File Systems
DTSTART:20250529T110000Z
DTEND:20250529T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nHashing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250530T090000Z
DTEND:20250530T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250530T110000Z
DTEND:20250530T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250530T090000Z
DTEND:20250530T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250530T110000Z
DTEND:20250530T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250530T090000Z
DTEND:20250530T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250530T110000Z
DTEND:20250530T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250530T090000Z
DTEND:20250530T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250530T110000Z
DTEND:20250530T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250530T090000Z
DTEND:20250530T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250530T110000Z
DTEND:20250530T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250530T090000Z
DTEND:20250530T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250530T110000Z
DTEND:20250530T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCommon subexpression elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250530T090000Z
DTEND:20250530T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250530T110000Z
DTEND:20250530T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow equations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250530T090000Z
DTEND:20250530T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250530T110000Z
DTEND:20250530T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250531T090000Z
DTEND:20250531T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250531T110000Z
DTEND:20250531T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250531T090000Z
DTEND:20250531T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250531T110000Z
DTEND:20250531T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250531T090000Z
DTEND:20250531T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250531T110000Z
DTEND:20250531T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250531T090000Z
DTEND:20250531T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250531T110000Z
DTEND:20250531T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250531T090000Z
DTEND:20250531T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250531T110000Z
DTEND:20250531T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250531T090000Z
DTEND:20250531T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250531T110000Z
DTEND:20250531T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCommon subexpression elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250531T090000Z
DTEND:20250531T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250531T110000Z
DTEND:20250531T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow equations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250531T090000Z
DTEND:20250531T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250531T110000Z
DTEND:20250531T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250601T090000Z
DTEND:20250601T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250601T110000Z
DTEND:20250601T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250601T090000Z
DTEND:20250601T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250601T110000Z
DTEND:20250601T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250601T090000Z
DTEND:20250601T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250601T110000Z
DTEND:20250601T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250601T090000Z
DTEND:20250601T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250601T110000Z
DTEND:20250601T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250601T090000Z
DTEND:20250601T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250601T110000Z
DTEND:20250601T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250601T090000Z
DTEND:20250601T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250601T110000Z
DTEND:20250601T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCommon subexpression elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250601T090000Z
DTEND:20250601T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250601T110000Z
DTEND:20250601T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow equations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250601T090000Z
DTEND:20250601T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250601T110000Z
DTEND:20250601T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250602T090000Z
DTEND:20250602T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250602T110000Z
DTEND:20250602T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250602T090000Z
DTEND:20250602T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250602T110000Z
DTEND:20250602T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250602T090000Z
DTEND:20250602T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250602T110000Z
DTEND:20250602T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250602T090000Z
DTEND:20250602T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250602T110000Z
DTEND:20250602T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250602T090000Z
DTEND:20250602T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250602T110000Z
DTEND:20250602T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250602T090000Z
DTEND:20250602T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250602T110000Z
DTEND:20250602T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCommon subexpression elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250602T090000Z
DTEND:20250602T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250602T110000Z
DTEND:20250602T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow equations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250602T090000Z
DTEND:20250602T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250602T110000Z
DTEND:20250602T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250603T090000Z
DTEND:20250603T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250603T110000Z
DTEND:20250603T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250603T090000Z
DTEND:20250603T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250603T110000Z
DTEND:20250603T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250603T090000Z
DTEND:20250603T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250603T110000Z
DTEND:20250603T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250603T090000Z
DTEND:20250603T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250603T110000Z
DTEND:20250603T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250603T090000Z
DTEND:20250603T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250603T110000Z
DTEND:20250603T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250603T090000Z
DTEND:20250603T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250603T110000Z
DTEND:20250603T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCommon subexpression elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250603T090000Z
DTEND:20250603T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250603T110000Z
DTEND:20250603T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow equations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250603T090000Z
DTEND:20250603T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250603T110000Z
DTEND:20250603T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250604T090000Z
DTEND:20250604T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250604T110000Z
DTEND:20250604T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250604T090000Z
DTEND:20250604T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250604T110000Z
DTEND:20250604T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250604T090000Z
DTEND:20250604T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250604T110000Z
DTEND:20250604T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250604T090000Z
DTEND:20250604T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250604T110000Z
DTEND:20250604T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250604T090000Z
DTEND:20250604T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250604T110000Z
DTEND:20250604T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250604T090000Z
DTEND:20250604T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250604T110000Z
DTEND:20250604T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCommon subexpression elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250604T090000Z
DTEND:20250604T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250604T110000Z
DTEND:20250604T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow equations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250604T090000Z
DTEND:20250604T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250604T110000Z
DTEND:20250604T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250605T090000Z
DTEND:20250605T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250605T110000Z
DTEND:20250605T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250605T090000Z
DTEND:20250605T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250605T110000Z
DTEND:20250605T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250605T090000Z
DTEND:20250605T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250605T110000Z
DTEND:20250605T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250605T090000Z
DTEND:20250605T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250605T110000Z
DTEND:20250605T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250605T090000Z
DTEND:20250605T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250605T110000Z
DTEND:20250605T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250605T090000Z
DTEND:20250605T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250605T110000Z
DTEND:20250605T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCommon subexpression elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250605T090000Z
DTEND:20250605T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250605T110000Z
DTEND:20250605T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow equations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250605T090000Z
DTEND:20250605T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250605T110000Z
DTEND:20250605T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250606T090000Z
DTEND:20250606T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250606T110000Z
DTEND:20250606T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250606T090000Z
DTEND:20250606T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250606T110000Z
DTEND:20250606T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250606T090000Z
DTEND:20250606T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250606T110000Z
DTEND:20250606T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIntermediate code generation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Runtime Environments
DTSTART:20250606T090000Z
DTEND:20250606T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nActivation records
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250606T110000Z
DTEND:20250606T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMemory management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250606T090000Z
DTEND:20250606T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: CPU and I/O Scheduling
DTSTART:20250606T110000Z
DTEND:20250606T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Local Optimization
DTSTART:20250606T090000Z
DTEND:20250606T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConstant folding
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Memory Management
DTSTART:20250606T110000Z
DTEND:20250606T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCommon subexpression elimination
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Data Flow Analysis
DTSTART:20250606T090000Z
DTEND:20250606T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graphs
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Virtual Memory
DTSTART:20250606T110000Z
DTEND:20250606T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow equations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model
DTSTART:20250606T090000Z
DTEND:20250606T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational Model
DTSTART:20250606T110000Z
DTEND:20250606T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of layering
DTSTART:20250607T090000Z
DTEND:20250607T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP model layers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of packet\, circuit\, and virtual
  circuit-switching
DTSTART:20250607T110000Z
DTEND:20250607T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferences between packet\, circuit\, and virt
 ual circuit switching
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data link layer
DTSTART:20250607T090000Z
DTEND:20250607T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFraming\, error detection\, MAC\, Ethernet brid
 ging
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing protocols
DTSTART:20250607T110000Z
DTEND:20250607T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nShortest path\, flooding\, distance vector\, li
 nk-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Fragmentation and IP addressing
DTSTART:20250607T090000Z
DTEND:20250607T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIPv4\, CIDR notation\, fragmentation process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of IP support protocols
DTSTART:20250607T110000Z
DTEND:20250607T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nARP\, DHCP\, ICMP functionality
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Address Translation (NAT)
DTSTART:20250607T090000Z
DTEND:20250607T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNAT functionality and benefits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport layer
DTSTART:20250607T110000Z
DTEND:20250607T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control\, congestion control\, UDP\, TCP\,
  sockets
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application layer protocols
DTSTART:20250607T090000Z
DTEND:20250607T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDNS\, SMTP\, HTTP\, FTP\, Email protocols
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-model
DTSTART:20250607T090000Z
DTEND:20250607T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities\, attributes\, relationships
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational model
DTSTART:20250607T110000Z
DTEND:20250607T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra\, tuple calculus\, SQL
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity constraints\, normal forms
DTSTART:20250607T090000Z
DTEND:20250607T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent normal forms and their purpose
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File organization\, indexing
DTSTART:20250607T110000Z
DTEND:20250607T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB and B+ trees\, indexing methods
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and concurrency control
DTSTART:20250607T090000Z
DTEND:20250607T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties\, concurrency control technique
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of layering
DTSTART:20250608T090000Z
DTEND:20250608T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP model layers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of packet\, circuit\, and virtual
  circuit-switching
DTSTART:20250608T110000Z
DTEND:20250608T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferences between packet\, circuit\, and virt
 ual circuit switching
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data link layer
DTSTART:20250608T090000Z
DTEND:20250608T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFraming\, error detection\, MAC\, Ethernet brid
 ging
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing protocols
DTSTART:20250608T110000Z
DTEND:20250608T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nShortest path\, flooding\, distance vector\, li
 nk-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Fragmentation and IP addressing
DTSTART:20250608T090000Z
DTEND:20250608T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIPv4\, CIDR notation\, fragmentation process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of IP support protocols
DTSTART:20250608T110000Z
DTEND:20250608T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nARP\, DHCP\, ICMP functionality
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Address Translation (NAT)
DTSTART:20250608T090000Z
DTEND:20250608T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNAT functionality and benefits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport layer
DTSTART:20250608T110000Z
DTEND:20250608T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control\, congestion control\, UDP\, TCP\,
  sockets
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application layer protocols
DTSTART:20250608T090000Z
DTEND:20250608T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDNS\, SMTP\, HTTP\, FTP\, Email protocols
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-model
DTSTART:20250608T090000Z
DTEND:20250608T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities\, attributes\, relationships
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational model
DTSTART:20250608T110000Z
DTEND:20250608T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra\, tuple calculus\, SQL
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity constraints\, normal forms
DTSTART:20250608T090000Z
DTEND:20250608T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent normal forms and their purpose
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File organization\, indexing
DTSTART:20250608T110000Z
DTEND:20250608T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB and B+ trees\, indexing methods
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and concurrency control
DTSTART:20250608T090000Z
DTEND:20250608T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties\, concurrency control technique
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of layering
DTSTART:20250609T090000Z
DTEND:20250609T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP model layers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of packet\, circuit\, and virtual
  circuit-switching
DTSTART:20250609T110000Z
DTEND:20250609T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferences between packet\, circuit\, and virt
 ual circuit switching
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data link layer
DTSTART:20250609T090000Z
DTEND:20250609T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFraming\, error detection\, MAC\, Ethernet brid
 ging
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing protocols
DTSTART:20250609T110000Z
DTEND:20250609T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nShortest path\, flooding\, distance vector\, li
 nk-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Fragmentation and IP addressing
DTSTART:20250609T090000Z
DTEND:20250609T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIPv4\, CIDR notation\, fragmentation process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of IP support protocols
DTSTART:20250609T110000Z
DTEND:20250609T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nARP\, DHCP\, ICMP functionality
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Address Translation (NAT)
DTSTART:20250609T090000Z
DTEND:20250609T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNAT functionality and benefits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport layer
DTSTART:20250609T110000Z
DTEND:20250609T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control\, congestion control\, UDP\, TCP\,
  sockets
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application layer protocols
DTSTART:20250609T090000Z
DTEND:20250609T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDNS\, SMTP\, HTTP\, FTP\, Email protocols
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-model
DTSTART:20250609T090000Z
DTEND:20250609T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities\, attributes\, relationships
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational model
DTSTART:20250609T110000Z
DTEND:20250609T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra\, tuple calculus\, SQL
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity constraints\, normal forms
DTSTART:20250609T090000Z
DTEND:20250609T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent normal forms and their purpose
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File organization\, indexing
DTSTART:20250609T110000Z
DTEND:20250609T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB and B+ trees\, indexing methods
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and concurrency control
DTSTART:20250609T090000Z
DTEND:20250609T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties\, concurrency control technique
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of layering
DTSTART:20250610T090000Z
DTEND:20250610T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP model layers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of packet\, circuit\, and virtual
  circuit-switching
DTSTART:20250610T110000Z
DTEND:20250610T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferences between packet\, circuit\, and virt
 ual circuit switching
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data link layer
DTSTART:20250610T090000Z
DTEND:20250610T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFraming\, error detection\, MAC\, Ethernet brid
 ging
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing protocols
DTSTART:20250610T110000Z
DTEND:20250610T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nShortest path\, flooding\, distance vector\, li
 nk-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Fragmentation and IP addressing
DTSTART:20250610T090000Z
DTEND:20250610T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIPv4\, CIDR notation\, fragmentation process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of IP support protocols
DTSTART:20250610T110000Z
DTEND:20250610T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nARP\, DHCP\, ICMP functionality
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Address Translation (NAT)
DTSTART:20250610T090000Z
DTEND:20250610T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNAT functionality and benefits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport layer
DTSTART:20250610T110000Z
DTEND:20250610T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control\, congestion control\, UDP\, TCP\,
  sockets
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application layer protocols
DTSTART:20250610T090000Z
DTEND:20250610T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDNS\, SMTP\, HTTP\, FTP\, Email protocols
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-model
DTSTART:20250610T090000Z
DTEND:20250610T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities\, attributes\, relationships
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational model
DTSTART:20250610T110000Z
DTEND:20250610T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra\, tuple calculus\, SQL
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity constraints\, normal forms
DTSTART:20250610T090000Z
DTEND:20250610T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent normal forms and their purpose
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File organization\, indexing
DTSTART:20250610T110000Z
DTEND:20250610T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB and B+ trees\, indexing methods
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and concurrency control
DTSTART:20250610T090000Z
DTEND:20250610T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties\, concurrency control technique
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of layering
DTSTART:20250611T090000Z
DTEND:20250611T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP model layers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of packet\, circuit\, and virtual
  circuit-switching
DTSTART:20250611T110000Z
DTEND:20250611T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferences between packet\, circuit\, and virt
 ual circuit switching
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data link layer
DTSTART:20250611T090000Z
DTEND:20250611T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFraming\, error detection\, MAC\, Ethernet brid
 ging
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing protocols
DTSTART:20250611T110000Z
DTEND:20250611T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nShortest path\, flooding\, distance vector\, li
 nk-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Fragmentation and IP addressing
DTSTART:20250611T090000Z
DTEND:20250611T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIPv4\, CIDR notation\, fragmentation process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of IP support protocols
DTSTART:20250611T110000Z
DTEND:20250611T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nARP\, DHCP\, ICMP functionality
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Address Translation (NAT)
DTSTART:20250611T090000Z
DTEND:20250611T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNAT functionality and benefits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport layer
DTSTART:20250611T110000Z
DTEND:20250611T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control\, congestion control\, UDP\, TCP\,
  sockets
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application layer protocols
DTSTART:20250611T090000Z
DTEND:20250611T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDNS\, SMTP\, HTTP\, FTP\, Email protocols
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-model
DTSTART:20250611T090000Z
DTEND:20250611T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities\, attributes\, relationships
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational model
DTSTART:20250611T110000Z
DTEND:20250611T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra\, tuple calculus\, SQL
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity constraints\, normal forms
DTSTART:20250611T090000Z
DTEND:20250611T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent normal forms and their purpose
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File organization\, indexing
DTSTART:20250611T110000Z
DTEND:20250611T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB and B+ trees\, indexing methods
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and concurrency control
DTSTART:20250611T090000Z
DTEND:20250611T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties\, concurrency control technique
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of layering
DTSTART:20250612T090000Z
DTEND:20250612T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP model layers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of packet\, circuit\, and virtual
  circuit-switching
DTSTART:20250612T110000Z
DTEND:20250612T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferences between packet\, circuit\, and virt
 ual circuit switching
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data link layer
DTSTART:20250612T090000Z
DTEND:20250612T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFraming\, error detection\, MAC\, Ethernet brid
 ging
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing protocols
DTSTART:20250612T110000Z
DTEND:20250612T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nShortest path\, flooding\, distance vector\, li
 nk-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Fragmentation and IP addressing
DTSTART:20250612T090000Z
DTEND:20250612T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIPv4\, CIDR notation\, fragmentation process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of IP support protocols
DTSTART:20250612T110000Z
DTEND:20250612T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nARP\, DHCP\, ICMP functionality
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Address Translation (NAT)
DTSTART:20250612T090000Z
DTEND:20250612T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNAT functionality and benefits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport layer
DTSTART:20250612T110000Z
DTEND:20250612T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control\, congestion control\, UDP\, TCP\,
  sockets
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application layer protocols
DTSTART:20250612T090000Z
DTEND:20250612T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDNS\, SMTP\, HTTP\, FTP\, Email protocols
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-model
DTSTART:20250612T090000Z
DTEND:20250612T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities\, attributes\, relationships
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational model
DTSTART:20250612T110000Z
DTEND:20250612T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra\, tuple calculus\, SQL
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity constraints\, normal forms
DTSTART:20250612T090000Z
DTEND:20250612T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent normal forms and their purpose
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File organization\, indexing
DTSTART:20250612T110000Z
DTEND:20250612T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB and B+ trees\, indexing methods
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and concurrency control
DTSTART:20250612T090000Z
DTEND:20250612T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties\, concurrency control technique
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of layering
DTSTART:20250613T090000Z
DTEND:20250613T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP model layers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of packet\, circuit\, and virtual
  circuit-switching
DTSTART:20250613T110000Z
DTEND:20250613T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferences between packet\, circuit\, and virt
 ual circuit switching
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data link layer
DTSTART:20250613T090000Z
DTEND:20250613T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFraming\, error detection\, MAC\, Ethernet brid
 ging
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing protocols
DTSTART:20250613T110000Z
DTEND:20250613T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nShortest path\, flooding\, distance vector\, li
 nk-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Fragmentation and IP addressing
DTSTART:20250613T090000Z
DTEND:20250613T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIPv4\, CIDR notation\, fragmentation process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of IP support protocols
DTSTART:20250613T110000Z
DTEND:20250613T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nARP\, DHCP\, ICMP functionality
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Address Translation (NAT)
DTSTART:20250613T090000Z
DTEND:20250613T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNAT functionality and benefits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport layer
DTSTART:20250613T110000Z
DTEND:20250613T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control\, congestion control\, UDP\, TCP\,
  sockets
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application layer protocols
DTSTART:20250613T090000Z
DTEND:20250613T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDNS\, SMTP\, HTTP\, FTP\, Email protocols
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-model
DTSTART:20250613T090000Z
DTEND:20250613T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities\, attributes\, relationships
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational model
DTSTART:20250613T110000Z
DTEND:20250613T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra\, tuple calculus\, SQL
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity constraints\, normal forms
DTSTART:20250613T090000Z
DTEND:20250613T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent normal forms and their purpose
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File organization\, indexing
DTSTART:20250613T110000Z
DTEND:20250613T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB and B+ trees\, indexing methods
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and concurrency control
DTSTART:20250613T090000Z
DTEND:20250613T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties\, concurrency control technique
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of layering
DTSTART:20250614T090000Z
DTEND:20250614T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nOSI and TCP/IP model layers
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of packet\, circuit\, and virtual
  circuit-switching
DTSTART:20250614T110000Z
DTEND:20250614T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferences between packet\, circuit\, and virt
 ual circuit switching
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data link layer
DTSTART:20250614T090000Z
DTEND:20250614T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFraming\, error detection\, MAC\, Ethernet brid
 ging
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing protocols
DTSTART:20250614T110000Z
DTEND:20250614T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nShortest path\, flooding\, distance vector\, li
 nk-state routing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Fragmentation and IP addressing
DTSTART:20250614T090000Z
DTEND:20250614T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nIPv4\, CIDR notation\, fragmentation process
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Basics of IP support protocols
DTSTART:20250614T110000Z
DTEND:20250614T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nARP\, DHCP\, ICMP functionality
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Address Translation (NAT)
DTSTART:20250614T090000Z
DTEND:20250614T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNAT functionality and benefits
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport layer
DTSTART:20250614T110000Z
DTEND:20250614T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control\, congestion control\, UDP\, TCP\,
  sockets
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application layer protocols
DTSTART:20250614T090000Z
DTEND:20250614T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDNS\, SMTP\, HTTP\, FTP\, Email protocols
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-model
DTSTART:20250614T090000Z
DTEND:20250614T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntities\, attributes\, relationships
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Relational model
DTSTART:20250614T110000Z
DTEND:20250614T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra\, tuple calculus\, SQL
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity constraints\, normal forms
DTSTART:20250614T090000Z
DTEND:20250614T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDifferent normal forms and their purpose
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File organization\, indexing
DTSTART:20250614T110000Z
DTEND:20250614T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB and B+ trees\, indexing methods
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and concurrency control
DTSTART:20250614T090000Z
DTEND:20250614T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties\, concurrency control technique
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250615T090000Z
DTEND:20250615T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250615T110000Z
DTEND:20250615T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250615T090000Z
DTEND:20250615T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250615T110000Z
DTEND:20250615T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250615T090000Z
DTEND:20250615T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250615T110000Z
DTEND:20250615T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAbstract syntax tree
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Optimization
DTSTART:20250615T090000Z
DTEND:20250615T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250615T110000Z
DTEND:20250615T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graph
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER Model and Relational Model
DTSTART:20250615T090000Z
DTEND:20250615T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250615T110000Z
DTEND:20250615T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: SQL and Normalization
DTSTART:20250615T090000Z
DTEND:20250615T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSQL queries
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250615T110000Z
DTEND:20250615T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormal forms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Indexing and Transactions
DTSTART:20250615T090000Z
DTEND:20250615T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250615T110000Z
DTEND:20250615T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport and Application Layer
DTSTART:20250615T090000Z
DTEND:20250615T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250616T090000Z
DTEND:20250616T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250616T110000Z
DTEND:20250616T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250616T090000Z
DTEND:20250616T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250616T110000Z
DTEND:20250616T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250616T090000Z
DTEND:20250616T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250616T110000Z
DTEND:20250616T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAbstract syntax tree
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Optimization
DTSTART:20250616T090000Z
DTEND:20250616T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250616T110000Z
DTEND:20250616T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graph
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER Model and Relational Model
DTSTART:20250616T090000Z
DTEND:20250616T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250616T110000Z
DTEND:20250616T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: SQL and Normalization
DTSTART:20250616T090000Z
DTEND:20250616T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSQL queries
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250616T110000Z
DTEND:20250616T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormal forms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Indexing and Transactions
DTSTART:20250616T090000Z
DTEND:20250616T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250616T110000Z
DTEND:20250616T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport and Application Layer
DTSTART:20250616T090000Z
DTEND:20250616T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250617T090000Z
DTEND:20250617T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250617T110000Z
DTEND:20250617T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250617T090000Z
DTEND:20250617T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250617T110000Z
DTEND:20250617T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250617T090000Z
DTEND:20250617T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250617T110000Z
DTEND:20250617T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAbstract syntax tree
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Optimization
DTSTART:20250617T090000Z
DTEND:20250617T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250617T110000Z
DTEND:20250617T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graph
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER Model and Relational Model
DTSTART:20250617T090000Z
DTEND:20250617T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250617T110000Z
DTEND:20250617T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: SQL and Normalization
DTSTART:20250617T090000Z
DTEND:20250617T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSQL queries
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250617T110000Z
DTEND:20250617T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormal forms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Indexing and Transactions
DTSTART:20250617T090000Z
DTEND:20250617T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250617T110000Z
DTEND:20250617T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport and Application Layer
DTSTART:20250617T090000Z
DTEND:20250617T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250618T090000Z
DTEND:20250618T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250618T110000Z
DTEND:20250618T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250618T090000Z
DTEND:20250618T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250618T110000Z
DTEND:20250618T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250618T090000Z
DTEND:20250618T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250618T110000Z
DTEND:20250618T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAbstract syntax tree
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Optimization
DTSTART:20250618T090000Z
DTEND:20250618T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250618T110000Z
DTEND:20250618T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graph
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER Model and Relational Model
DTSTART:20250618T090000Z
DTEND:20250618T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250618T110000Z
DTEND:20250618T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: SQL and Normalization
DTSTART:20250618T090000Z
DTEND:20250618T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSQL queries
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250618T110000Z
DTEND:20250618T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormal forms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Indexing and Transactions
DTSTART:20250618T090000Z
DTEND:20250618T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250618T110000Z
DTEND:20250618T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport and Application Layer
DTSTART:20250618T090000Z
DTEND:20250618T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250619T090000Z
DTEND:20250619T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250619T110000Z
DTEND:20250619T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250619T090000Z
DTEND:20250619T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250619T110000Z
DTEND:20250619T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250619T090000Z
DTEND:20250619T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250619T110000Z
DTEND:20250619T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAbstract syntax tree
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Optimization
DTSTART:20250619T090000Z
DTEND:20250619T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250619T110000Z
DTEND:20250619T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graph
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER Model and Relational Model
DTSTART:20250619T090000Z
DTEND:20250619T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250619T110000Z
DTEND:20250619T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: SQL and Normalization
DTSTART:20250619T090000Z
DTEND:20250619T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSQL queries
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250619T110000Z
DTEND:20250619T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormal forms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Indexing and Transactions
DTSTART:20250619T090000Z
DTEND:20250619T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250619T110000Z
DTEND:20250619T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport and Application Layer
DTSTART:20250619T090000Z
DTEND:20250619T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250620T090000Z
DTEND:20250620T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250620T110000Z
DTEND:20250620T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250620T090000Z
DTEND:20250620T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250620T110000Z
DTEND:20250620T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250620T090000Z
DTEND:20250620T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250620T110000Z
DTEND:20250620T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAbstract syntax tree
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Optimization
DTSTART:20250620T090000Z
DTEND:20250620T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250620T110000Z
DTEND:20250620T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graph
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER Model and Relational Model
DTSTART:20250620T090000Z
DTEND:20250620T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250620T110000Z
DTEND:20250620T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: SQL and Normalization
DTSTART:20250620T090000Z
DTEND:20250620T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSQL queries
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250620T110000Z
DTEND:20250620T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormal forms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Indexing and Transactions
DTSTART:20250620T090000Z
DTEND:20250620T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250620T110000Z
DTEND:20250620T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport and Application Layer
DTSTART:20250620T090000Z
DTEND:20250620T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250621T090000Z
DTEND:20250621T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250621T110000Z
DTEND:20250621T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250621T090000Z
DTEND:20250621T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250621T110000Z
DTEND:20250621T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250621T090000Z
DTEND:20250621T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250621T110000Z
DTEND:20250621T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAbstract syntax tree
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Optimization
DTSTART:20250621T090000Z
DTEND:20250621T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250621T110000Z
DTEND:20250621T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graph
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER Model and Relational Model
DTSTART:20250621T090000Z
DTEND:20250621T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250621T110000Z
DTEND:20250621T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: SQL and Normalization
DTSTART:20250621T090000Z
DTEND:20250621T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSQL queries
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250621T110000Z
DTEND:20250621T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormal forms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Indexing and Transactions
DTSTART:20250621T090000Z
DTEND:20250621T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250621T110000Z
DTEND:20250621T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport and Application Layer
DTSTART:20250621T090000Z
DTEND:20250621T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250622T090000Z
DTEND:20250622T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250622T110000Z
DTEND:20250622T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250622T090000Z
DTEND:20250622T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and IPC
DTSTART:20250622T110000Z
DTEND:20250622T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Intermediate Code Generation
DTSTART:20250622T090000Z
DTEND:20250622T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nThree-address code
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250622T110000Z
DTEND:20250622T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAbstract syntax tree
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Optimization
DTSTART:20250622T090000Z
DTEND:20250622T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nData flow analysis
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Deadlock
DTSTART:20250622T110000Z
DTEND:20250622T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nControl flow graph
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER Model and Relational Model
DTSTART:20250622T090000Z
DTEND:20250622T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Layering and Switching
DTSTART:20250622T110000Z
DTEND:20250622T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: SQL and Normalization
DTSTART:20250622T090000Z
DTEND:20250622T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSQL queries
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250622T110000Z
DTEND:20250622T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormal forms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Indexing and Transactions
DTSTART:20250622T090000Z
DTEND:20250622T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250622T110000Z
DTEND:20250622T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport and Application Layer
DTSTART:20250622T090000Z
DTEND:20250622T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTCP
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Discrete Mathematics
DTSTART:20250623T090000Z
DTEND:20250623T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLogical Equivalences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250623T110000Z
DTEND:20250623T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPartial Orders and Lattices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Linear Algebra
DTSTART:20250623T090000Z
DTEND:20250623T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix Operations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250623T110000Z
DTEND:20250623T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue Decomposition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Calculus
DTSTART:20250623T090000Z
DTEND:20250623T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDerivatives and Integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250623T110000Z
DTEND:20250623T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMaxima and Minima
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Probability and Statistics
DTSTART:20250623T090000Z
DTEND:20250623T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability Distributions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250623T110000Z
DTEND:20250623T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional Probability
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250623T090000Z
DTEND:20250623T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular Expressions and Languages
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250623T090000Z
DTEND:20250623T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical Analysis and Parsing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250623T090000Z
DTEND:20250623T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and Threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250623T090000Z
DTEND:20250623T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250623T110000Z
DTEND:20250623T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction Management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Discrete Mathematics
DTSTART:20250624T090000Z
DTEND:20250624T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLogical Equivalences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250624T110000Z
DTEND:20250624T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPartial Orders and Lattices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Linear Algebra
DTSTART:20250624T090000Z
DTEND:20250624T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix Operations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250624T110000Z
DTEND:20250624T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue Decomposition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Calculus
DTSTART:20250624T090000Z
DTEND:20250624T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDerivatives and Integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250624T110000Z
DTEND:20250624T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMaxima and Minima
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Probability and Statistics
DTSTART:20250624T090000Z
DTEND:20250624T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability Distributions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250624T110000Z
DTEND:20250624T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional Probability
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250624T090000Z
DTEND:20250624T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular Expressions and Languages
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250624T090000Z
DTEND:20250624T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical Analysis and Parsing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250624T090000Z
DTEND:20250624T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and Threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250624T090000Z
DTEND:20250624T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250624T110000Z
DTEND:20250624T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction Management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Discrete Mathematics
DTSTART:20250625T090000Z
DTEND:20250625T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLogical Equivalences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250625T110000Z
DTEND:20250625T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPartial Orders and Lattices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Linear Algebra
DTSTART:20250625T090000Z
DTEND:20250625T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix Operations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250625T110000Z
DTEND:20250625T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue Decomposition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Calculus
DTSTART:20250625T090000Z
DTEND:20250625T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDerivatives and Integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250625T110000Z
DTEND:20250625T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMaxima and Minima
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Probability and Statistics
DTSTART:20250625T090000Z
DTEND:20250625T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability Distributions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250625T110000Z
DTEND:20250625T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional Probability
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250625T090000Z
DTEND:20250625T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular Expressions and Languages
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250625T090000Z
DTEND:20250625T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical Analysis and Parsing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250625T090000Z
DTEND:20250625T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and Threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250625T090000Z
DTEND:20250625T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250625T110000Z
DTEND:20250625T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction Management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Discrete Mathematics
DTSTART:20250626T090000Z
DTEND:20250626T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLogical Equivalences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250626T110000Z
DTEND:20250626T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPartial Orders and Lattices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Linear Algebra
DTSTART:20250626T090000Z
DTEND:20250626T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix Operations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250626T110000Z
DTEND:20250626T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue Decomposition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Calculus
DTSTART:20250626T090000Z
DTEND:20250626T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDerivatives and Integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250626T110000Z
DTEND:20250626T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMaxima and Minima
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Probability and Statistics
DTSTART:20250626T090000Z
DTEND:20250626T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability Distributions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250626T110000Z
DTEND:20250626T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional Probability
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250626T090000Z
DTEND:20250626T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular Expressions and Languages
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250626T090000Z
DTEND:20250626T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical Analysis and Parsing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250626T090000Z
DTEND:20250626T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and Threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250626T090000Z
DTEND:20250626T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250626T110000Z
DTEND:20250626T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction Management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Discrete Mathematics
DTSTART:20250627T090000Z
DTEND:20250627T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLogical Equivalences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250627T110000Z
DTEND:20250627T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPartial Orders and Lattices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Linear Algebra
DTSTART:20250627T090000Z
DTEND:20250627T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix Operations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250627T110000Z
DTEND:20250627T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue Decomposition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Calculus
DTSTART:20250627T090000Z
DTEND:20250627T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDerivatives and Integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250627T110000Z
DTEND:20250627T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMaxima and Minima
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Probability and Statistics
DTSTART:20250627T090000Z
DTEND:20250627T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability Distributions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250627T110000Z
DTEND:20250627T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional Probability
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250627T090000Z
DTEND:20250627T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular Expressions and Languages
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250627T090000Z
DTEND:20250627T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical Analysis and Parsing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250627T090000Z
DTEND:20250627T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and Threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250627T090000Z
DTEND:20250627T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250627T110000Z
DTEND:20250627T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction Management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Discrete Mathematics
DTSTART:20250628T090000Z
DTEND:20250628T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLogical Equivalences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250628T110000Z
DTEND:20250628T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPartial Orders and Lattices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Linear Algebra
DTSTART:20250628T090000Z
DTEND:20250628T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix Operations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250628T110000Z
DTEND:20250628T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue Decomposition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Calculus
DTSTART:20250628T090000Z
DTEND:20250628T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDerivatives and Integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250628T110000Z
DTEND:20250628T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMaxima and Minima
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Probability and Statistics
DTSTART:20250628T090000Z
DTEND:20250628T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability Distributions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250628T110000Z
DTEND:20250628T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional Probability
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250628T090000Z
DTEND:20250628T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular Expressions and Languages
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250628T090000Z
DTEND:20250628T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical Analysis and Parsing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250628T090000Z
DTEND:20250628T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and Threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250628T090000Z
DTEND:20250628T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250628T110000Z
DTEND:20250628T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction Management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Discrete Mathematics
DTSTART:20250629T090000Z
DTEND:20250629T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLogical Equivalences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250629T110000Z
DTEND:20250629T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPartial Orders and Lattices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Linear Algebra
DTSTART:20250629T090000Z
DTEND:20250629T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix Operations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250629T110000Z
DTEND:20250629T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue Decomposition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Calculus
DTSTART:20250629T090000Z
DTEND:20250629T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDerivatives and Integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250629T110000Z
DTEND:20250629T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMaxima and Minima
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Probability and Statistics
DTSTART:20250629T090000Z
DTEND:20250629T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability Distributions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250629T110000Z
DTEND:20250629T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional Probability
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250629T090000Z
DTEND:20250629T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular Expressions and Languages
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250629T090000Z
DTEND:20250629T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical Analysis and Parsing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250629T090000Z
DTEND:20250629T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and Threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250629T090000Z
DTEND:20250629T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250629T110000Z
DTEND:20250629T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction Management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Discrete Mathematics
DTSTART:20250630T090000Z
DTEND:20250630T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLogical Equivalences
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic
DTSTART:20250630T110000Z
DTEND:20250630T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPartial Orders and Lattices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Linear Algebra
DTSTART:20250630T090000Z
DTEND:20250630T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix Operations
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture
DTSTART:20250630T110000Z
DTEND:20250630T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEigenvalue Decomposition
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Calculus
DTSTART:20250630T090000Z
DTEND:20250630T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nDerivatives and Integrals
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Programming and Data Structures
DTSTART:20250630T110000Z
DTEND:20250630T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMaxima and Minima
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Engineering Mathematics - Probability and Statistics
DTSTART:20250630T090000Z
DTEND:20250630T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability Distributions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Algorithms
DTSTART:20250630T110000Z
DTEND:20250630T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional Probability
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Theory of Computation
DTSTART:20250630T090000Z
DTEND:20250630T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular Expressions and Languages
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250630T090000Z
DTEND:20250630T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical Analysis and Parsing
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250630T090000Z
DTEND:20250630T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProcesses and Threads
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases
DTSTART:20250630T090000Z
DTEND:20250630T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks
DTSTART:20250630T110000Z
DTEND:20250630T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTransaction Management
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250701T090000Z
DTEND:20250701T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical connectives\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Boolean algebra
DTSTART:20250701T110000Z
DTEND:20250701T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra laws\, logic gates
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions\, partia
 l orders\, lattices
DTSTART:20250701T090000Z
DTEND:20250701T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Combinational and sequential circuits
DTSTART:20250701T110000Z
DTEND:20250701T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAdders\, multiplexers\, flip-flops\, state diag
 rams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250701T090000Z
DTEND:20250701T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant calculation\, i
 nverse of a matrix
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Minimization\, Number representations
DTSTART:20250701T110000Z
DTEND:20250701T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nKarnaugh maps\, two's complement\, floating-poi
 nt representation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations\, Eigenvalues an
 d eigenvectors
DTSTART:20250701T090000Z
DTEND:20250701T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, eigenvalues and eigenvec
 tors calculation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Machine instruction
 s and addressing modes
DTSTART:20250701T110000Z
DTEND:20250701T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInstruction formats\, addressing modes (direct\
 , indirect\, register)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250701T090000Z
DTEND:20250701T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit theorems\, continuity conditions\, differ
 entiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: ALU\, data-path\, a
 nd control unit
DTSTART:20250701T110000Z
DTEND:20250701T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nALU operations\, data path components\, control
  unit functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima\, Mean value theorem\, Integrat
 ion
DTSTART:20250701T090000Z
DTEND:20250701T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinding maxima and minima\, mean value theorem 
 applications\, integration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Instruction pipelin
 ing\, pipeline hazards
DTSTART:20250701T110000Z
DTEND:20250701T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPipeline stages\, data hazards\, control hazard
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables\, Mean\, media
 n\, mode\, standard deviation
DTSTART:20250701T090000Z
DTEND:20250701T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability distributions\, statistical measure
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Memory hierarchy
DTSTART:20250701T110000Z
DTEND:20250701T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCache organization\, memory management\, virtua
 l memory
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Conditional probability and Bay
 es theorem
DTSTART:20250701T090000Z
DTEND:20250701T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional probability calculations\, Bayes th
 eorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: I/O interface
DTSTART:20250701T110000Z
DTEND:20250701T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInterrupts\, DMA\, I/O devices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250702T090000Z
DTEND:20250702T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical connectives\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Boolean algebra
DTSTART:20250702T110000Z
DTEND:20250702T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra laws\, logic gates
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions\, partia
 l orders\, lattices
DTSTART:20250702T090000Z
DTEND:20250702T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Combinational and sequential circuits
DTSTART:20250702T110000Z
DTEND:20250702T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAdders\, multiplexers\, flip-flops\, state diag
 rams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250702T090000Z
DTEND:20250702T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant calculation\, i
 nverse of a matrix
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Minimization\, Number representations
DTSTART:20250702T110000Z
DTEND:20250702T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nKarnaugh maps\, two's complement\, floating-poi
 nt representation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations\, Eigenvalues an
 d eigenvectors
DTSTART:20250702T090000Z
DTEND:20250702T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, eigenvalues and eigenvec
 tors calculation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Machine instruction
 s and addressing modes
DTSTART:20250702T110000Z
DTEND:20250702T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInstruction formats\, addressing modes (direct\
 , indirect\, register)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250702T090000Z
DTEND:20250702T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit theorems\, continuity conditions\, differ
 entiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: ALU\, data-path\, a
 nd control unit
DTSTART:20250702T110000Z
DTEND:20250702T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nALU operations\, data path components\, control
  unit functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima\, Mean value theorem\, Integrat
 ion
DTSTART:20250702T090000Z
DTEND:20250702T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinding maxima and minima\, mean value theorem 
 applications\, integration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Instruction pipelin
 ing\, pipeline hazards
DTSTART:20250702T110000Z
DTEND:20250702T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPipeline stages\, data hazards\, control hazard
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables\, Mean\, media
 n\, mode\, standard deviation
DTSTART:20250702T090000Z
DTEND:20250702T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability distributions\, statistical measure
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Memory hierarchy
DTSTART:20250702T110000Z
DTEND:20250702T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCache organization\, memory management\, virtua
 l memory
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Conditional probability and Bay
 es theorem
DTSTART:20250702T090000Z
DTEND:20250702T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional probability calculations\, Bayes th
 eorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: I/O interface
DTSTART:20250702T110000Z
DTEND:20250702T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInterrupts\, DMA\, I/O devices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250703T090000Z
DTEND:20250703T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical connectives\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Boolean algebra
DTSTART:20250703T110000Z
DTEND:20250703T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra laws\, logic gates
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions\, partia
 l orders\, lattices
DTSTART:20250703T090000Z
DTEND:20250703T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Combinational and sequential circuits
DTSTART:20250703T110000Z
DTEND:20250703T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAdders\, multiplexers\, flip-flops\, state diag
 rams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250703T090000Z
DTEND:20250703T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant calculation\, i
 nverse of a matrix
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Minimization\, Number representations
DTSTART:20250703T110000Z
DTEND:20250703T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nKarnaugh maps\, two's complement\, floating-poi
 nt representation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations\, Eigenvalues an
 d eigenvectors
DTSTART:20250703T090000Z
DTEND:20250703T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, eigenvalues and eigenvec
 tors calculation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Machine instruction
 s and addressing modes
DTSTART:20250703T110000Z
DTEND:20250703T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInstruction formats\, addressing modes (direct\
 , indirect\, register)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250703T090000Z
DTEND:20250703T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit theorems\, continuity conditions\, differ
 entiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: ALU\, data-path\, a
 nd control unit
DTSTART:20250703T110000Z
DTEND:20250703T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nALU operations\, data path components\, control
  unit functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima\, Mean value theorem\, Integrat
 ion
DTSTART:20250703T090000Z
DTEND:20250703T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinding maxima and minima\, mean value theorem 
 applications\, integration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Instruction pipelin
 ing\, pipeline hazards
DTSTART:20250703T110000Z
DTEND:20250703T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPipeline stages\, data hazards\, control hazard
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables\, Mean\, media
 n\, mode\, standard deviation
DTSTART:20250703T090000Z
DTEND:20250703T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability distributions\, statistical measure
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Memory hierarchy
DTSTART:20250703T110000Z
DTEND:20250703T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCache organization\, memory management\, virtua
 l memory
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Conditional probability and Bay
 es theorem
DTSTART:20250703T090000Z
DTEND:20250703T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional probability calculations\, Bayes th
 eorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: I/O interface
DTSTART:20250703T110000Z
DTEND:20250703T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInterrupts\, DMA\, I/O devices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250704T090000Z
DTEND:20250704T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical connectives\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Boolean algebra
DTSTART:20250704T110000Z
DTEND:20250704T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra laws\, logic gates
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions\, partia
 l orders\, lattices
DTSTART:20250704T090000Z
DTEND:20250704T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Combinational and sequential circuits
DTSTART:20250704T110000Z
DTEND:20250704T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAdders\, multiplexers\, flip-flops\, state diag
 rams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250704T090000Z
DTEND:20250704T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant calculation\, i
 nverse of a matrix
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Minimization\, Number representations
DTSTART:20250704T110000Z
DTEND:20250704T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nKarnaugh maps\, two's complement\, floating-poi
 nt representation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations\, Eigenvalues an
 d eigenvectors
DTSTART:20250704T090000Z
DTEND:20250704T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, eigenvalues and eigenvec
 tors calculation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Machine instruction
 s and addressing modes
DTSTART:20250704T110000Z
DTEND:20250704T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInstruction formats\, addressing modes (direct\
 , indirect\, register)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250704T090000Z
DTEND:20250704T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit theorems\, continuity conditions\, differ
 entiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: ALU\, data-path\, a
 nd control unit
DTSTART:20250704T110000Z
DTEND:20250704T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nALU operations\, data path components\, control
  unit functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima\, Mean value theorem\, Integrat
 ion
DTSTART:20250704T090000Z
DTEND:20250704T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinding maxima and minima\, mean value theorem 
 applications\, integration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Instruction pipelin
 ing\, pipeline hazards
DTSTART:20250704T110000Z
DTEND:20250704T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPipeline stages\, data hazards\, control hazard
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables\, Mean\, media
 n\, mode\, standard deviation
DTSTART:20250704T090000Z
DTEND:20250704T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability distributions\, statistical measure
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Memory hierarchy
DTSTART:20250704T110000Z
DTEND:20250704T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCache organization\, memory management\, virtua
 l memory
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Conditional probability and Bay
 es theorem
DTSTART:20250704T090000Z
DTEND:20250704T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional probability calculations\, Bayes th
 eorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: I/O interface
DTSTART:20250704T110000Z
DTEND:20250704T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInterrupts\, DMA\, I/O devices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250705T090000Z
DTEND:20250705T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical connectives\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Boolean algebra
DTSTART:20250705T110000Z
DTEND:20250705T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra laws\, logic gates
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions\, partia
 l orders\, lattices
DTSTART:20250705T090000Z
DTEND:20250705T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Combinational and sequential circuits
DTSTART:20250705T110000Z
DTEND:20250705T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAdders\, multiplexers\, flip-flops\, state diag
 rams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250705T090000Z
DTEND:20250705T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant calculation\, i
 nverse of a matrix
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Minimization\, Number representations
DTSTART:20250705T110000Z
DTEND:20250705T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nKarnaugh maps\, two's complement\, floating-poi
 nt representation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations\, Eigenvalues an
 d eigenvectors
DTSTART:20250705T090000Z
DTEND:20250705T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, eigenvalues and eigenvec
 tors calculation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Machine instruction
 s and addressing modes
DTSTART:20250705T110000Z
DTEND:20250705T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInstruction formats\, addressing modes (direct\
 , indirect\, register)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250705T090000Z
DTEND:20250705T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit theorems\, continuity conditions\, differ
 entiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: ALU\, data-path\, a
 nd control unit
DTSTART:20250705T110000Z
DTEND:20250705T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nALU operations\, data path components\, control
  unit functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima\, Mean value theorem\, Integrat
 ion
DTSTART:20250705T090000Z
DTEND:20250705T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinding maxima and minima\, mean value theorem 
 applications\, integration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Instruction pipelin
 ing\, pipeline hazards
DTSTART:20250705T110000Z
DTEND:20250705T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPipeline stages\, data hazards\, control hazard
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables\, Mean\, media
 n\, mode\, standard deviation
DTSTART:20250705T090000Z
DTEND:20250705T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability distributions\, statistical measure
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Memory hierarchy
DTSTART:20250705T110000Z
DTEND:20250705T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCache organization\, memory management\, virtua
 l memory
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Conditional probability and Bay
 es theorem
DTSTART:20250705T090000Z
DTEND:20250705T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional probability calculations\, Bayes th
 eorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: I/O interface
DTSTART:20250705T110000Z
DTEND:20250705T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInterrupts\, DMA\, I/O devices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250706T090000Z
DTEND:20250706T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical connectives\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Boolean algebra
DTSTART:20250706T110000Z
DTEND:20250706T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra laws\, logic gates
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions\, partia
 l orders\, lattices
DTSTART:20250706T090000Z
DTEND:20250706T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Combinational and sequential circuits
DTSTART:20250706T110000Z
DTEND:20250706T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAdders\, multiplexers\, flip-flops\, state diag
 rams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250706T090000Z
DTEND:20250706T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant calculation\, i
 nverse of a matrix
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Minimization\, Number representations
DTSTART:20250706T110000Z
DTEND:20250706T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nKarnaugh maps\, two's complement\, floating-poi
 nt representation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations\, Eigenvalues an
 d eigenvectors
DTSTART:20250706T090000Z
DTEND:20250706T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, eigenvalues and eigenvec
 tors calculation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Machine instruction
 s and addressing modes
DTSTART:20250706T110000Z
DTEND:20250706T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInstruction formats\, addressing modes (direct\
 , indirect\, register)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250706T090000Z
DTEND:20250706T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit theorems\, continuity conditions\, differ
 entiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: ALU\, data-path\, a
 nd control unit
DTSTART:20250706T110000Z
DTEND:20250706T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nALU operations\, data path components\, control
  unit functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima\, Mean value theorem\, Integrat
 ion
DTSTART:20250706T090000Z
DTEND:20250706T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinding maxima and minima\, mean value theorem 
 applications\, integration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Instruction pipelin
 ing\, pipeline hazards
DTSTART:20250706T110000Z
DTEND:20250706T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPipeline stages\, data hazards\, control hazard
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables\, Mean\, media
 n\, mode\, standard deviation
DTSTART:20250706T090000Z
DTEND:20250706T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability distributions\, statistical measure
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Memory hierarchy
DTSTART:20250706T110000Z
DTEND:20250706T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCache organization\, memory management\, virtua
 l memory
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Conditional probability and Bay
 es theorem
DTSTART:20250706T090000Z
DTEND:20250706T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional probability calculations\, Bayes th
 eorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: I/O interface
DTSTART:20250706T110000Z
DTEND:20250706T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInterrupts\, DMA\, I/O devices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250707T090000Z
DTEND:20250707T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical connectives\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Boolean algebra
DTSTART:20250707T110000Z
DTEND:20250707T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra laws\, logic gates
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions\, partia
 l orders\, lattices
DTSTART:20250707T090000Z
DTEND:20250707T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Combinational and sequential circuits
DTSTART:20250707T110000Z
DTEND:20250707T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAdders\, multiplexers\, flip-flops\, state diag
 rams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250707T090000Z
DTEND:20250707T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant calculation\, i
 nverse of a matrix
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Minimization\, Number representations
DTSTART:20250707T110000Z
DTEND:20250707T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nKarnaugh maps\, two's complement\, floating-poi
 nt representation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations\, Eigenvalues an
 d eigenvectors
DTSTART:20250707T090000Z
DTEND:20250707T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, eigenvalues and eigenvec
 tors calculation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Machine instruction
 s and addressing modes
DTSTART:20250707T110000Z
DTEND:20250707T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInstruction formats\, addressing modes (direct\
 , indirect\, register)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250707T090000Z
DTEND:20250707T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit theorems\, continuity conditions\, differ
 entiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: ALU\, data-path\, a
 nd control unit
DTSTART:20250707T110000Z
DTEND:20250707T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nALU operations\, data path components\, control
  unit functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima\, Mean value theorem\, Integrat
 ion
DTSTART:20250707T090000Z
DTEND:20250707T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinding maxima and minima\, mean value theorem 
 applications\, integration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Instruction pipelin
 ing\, pipeline hazards
DTSTART:20250707T110000Z
DTEND:20250707T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPipeline stages\, data hazards\, control hazard
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables\, Mean\, media
 n\, mode\, standard deviation
DTSTART:20250707T090000Z
DTEND:20250707T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability distributions\, statistical measure
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Memory hierarchy
DTSTART:20250707T110000Z
DTEND:20250707T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCache organization\, memory management\, virtua
 l memory
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Conditional probability and Bay
 es theorem
DTSTART:20250707T090000Z
DTEND:20250707T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional probability calculations\, Bayes th
 eorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: I/O interface
DTSTART:20250707T110000Z
DTEND:20250707T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInterrupts\, DMA\, I/O devices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Propositional and first-order logic
DTSTART:20250708T090000Z
DTEND:20250708T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nTruth tables\, logical connectives\, quantifier
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Boolean algebra
DTSTART:20250708T110000Z
DTEND:20250708T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nBoolean algebra laws\, logic gates
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Discrete Mathematics: Sets\, relations\, functions\, partia
 l orders\, lattices
DTSTART:20250708T090000Z
DTEND:20250708T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSet operations\, relations properties\, functio
 n types\, lattice properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Combinational and sequential circuits
DTSTART:20250708T110000Z
DTEND:20250708T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAdders\, multiplexers\, flip-flops\, state diag
 rams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: Matrices\, determinants
DTSTART:20250708T090000Z
DTEND:20250708T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nMatrix operations\, determinant calculation\, i
 nverse of a matrix
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Digital Logic: Minimization\, Number representations
DTSTART:20250708T110000Z
DTEND:20250708T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nKarnaugh maps\, two's complement\, floating-poi
 nt representation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Linear Algebra: System of linear equations\, Eigenvalues an
 d eigenvectors
DTSTART:20250708T090000Z
DTEND:20250708T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nGaussian elimination\, eigenvalues and eigenvec
 tors calculation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Machine instruction
 s and addressing modes
DTSTART:20250708T110000Z
DTEND:20250708T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInstruction formats\, addressing modes (direct\
 , indirect\, register)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Limits\, continuity\, and differentiability
DTSTART:20250708T090000Z
DTEND:20250708T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLimit theorems\, continuity conditions\, differ
 entiation rules
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: ALU\, data-path\, a
 nd control unit
DTSTART:20250708T110000Z
DTEND:20250708T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nALU operations\, data path components\, control
  unit functions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Calculus: Maxima and minima\, Mean value theorem\, Integrat
 ion
DTSTART:20250708T090000Z
DTEND:20250708T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinding maxima and minima\, mean value theorem 
 applications\, integration techniques
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Instruction pipelin
 ing\, pipeline hazards
DTSTART:20250708T110000Z
DTEND:20250708T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nPipeline stages\, data hazards\, control hazard
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Random variables\, Mean\, media
 n\, mode\, standard deviation
DTSTART:20250708T090000Z
DTEND:20250708T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nProbability distributions\, statistical measure
 s
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: Memory hierarchy
DTSTART:20250708T110000Z
DTEND:20250708T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCache organization\, memory management\, virtua
 l memory
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Probability and Statistics: Conditional probability and Bay
 es theorem
DTSTART:20250708T090000Z
DTEND:20250708T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConditional probability calculations\, Bayes th
 eorem applications
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Organization and Architecture: I/O interface
DTSTART:20250708T110000Z
DTEND:20250708T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nInterrupts\, DMA\, I/O devices
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250709T090000Z
DTEND:20250709T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250709T110000Z
DTEND:20250709T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250709T090000Z
DTEND:20250709T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250709T110000Z
DTEND:20250709T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250709T090000Z
DTEND:20250709T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250709T110000Z
DTEND:20250709T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250709T090000Z
DTEND:20250709T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of Layering and Switching
DTSTART:20250709T110000Z
DTEND:20250709T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250709T090000Z
DTEND:20250709T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250709T110000Z
DTEND:20250709T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization forms (1NF\, 2NF\, 3NF\, BCNF)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250709T090000Z
DTEND:20250709T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250709T110000Z
DTEND:20250709T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and Concurrency Control
DTSTART:20250709T090000Z
DTEND:20250709T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250709T110000Z
DTEND:20250709T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConcurrency control mechanisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport Layer
DTSTART:20250709T090000Z
DTEND:20250709T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application Layer
DTSTART:20250709T110000Z
DTEND:20250709T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCongestion control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250710T090000Z
DTEND:20250710T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250710T110000Z
DTEND:20250710T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250710T090000Z
DTEND:20250710T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250710T110000Z
DTEND:20250710T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250710T090000Z
DTEND:20250710T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250710T110000Z
DTEND:20250710T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250710T090000Z
DTEND:20250710T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of Layering and Switching
DTSTART:20250710T110000Z
DTEND:20250710T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250710T090000Z
DTEND:20250710T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250710T110000Z
DTEND:20250710T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization forms (1NF\, 2NF\, 3NF\, BCNF)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250710T090000Z
DTEND:20250710T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250710T110000Z
DTEND:20250710T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and Concurrency Control
DTSTART:20250710T090000Z
DTEND:20250710T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250710T110000Z
DTEND:20250710T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConcurrency control mechanisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport Layer
DTSTART:20250710T090000Z
DTEND:20250710T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application Layer
DTSTART:20250710T110000Z
DTEND:20250710T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCongestion control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250711T090000Z
DTEND:20250711T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250711T110000Z
DTEND:20250711T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250711T090000Z
DTEND:20250711T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250711T110000Z
DTEND:20250711T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250711T090000Z
DTEND:20250711T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250711T110000Z
DTEND:20250711T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250711T090000Z
DTEND:20250711T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of Layering and Switching
DTSTART:20250711T110000Z
DTEND:20250711T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250711T090000Z
DTEND:20250711T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250711T110000Z
DTEND:20250711T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization forms (1NF\, 2NF\, 3NF\, BCNF)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250711T090000Z
DTEND:20250711T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250711T110000Z
DTEND:20250711T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and Concurrency Control
DTSTART:20250711T090000Z
DTEND:20250711T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250711T110000Z
DTEND:20250711T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConcurrency control mechanisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport Layer
DTSTART:20250711T090000Z
DTEND:20250711T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application Layer
DTSTART:20250711T110000Z
DTEND:20250711T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCongestion control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250712T090000Z
DTEND:20250712T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250712T110000Z
DTEND:20250712T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250712T090000Z
DTEND:20250712T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250712T110000Z
DTEND:20250712T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250712T090000Z
DTEND:20250712T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250712T110000Z
DTEND:20250712T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250712T090000Z
DTEND:20250712T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of Layering and Switching
DTSTART:20250712T110000Z
DTEND:20250712T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250712T090000Z
DTEND:20250712T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250712T110000Z
DTEND:20250712T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization forms (1NF\, 2NF\, 3NF\, BCNF)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250712T090000Z
DTEND:20250712T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250712T110000Z
DTEND:20250712T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and Concurrency Control
DTSTART:20250712T090000Z
DTEND:20250712T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250712T110000Z
DTEND:20250712T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConcurrency control mechanisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport Layer
DTSTART:20250712T090000Z
DTEND:20250712T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application Layer
DTSTART:20250712T110000Z
DTEND:20250712T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCongestion control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250713T090000Z
DTEND:20250713T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250713T110000Z
DTEND:20250713T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250713T090000Z
DTEND:20250713T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250713T110000Z
DTEND:20250713T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250713T090000Z
DTEND:20250713T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250713T110000Z
DTEND:20250713T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250713T090000Z
DTEND:20250713T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of Layering and Switching
DTSTART:20250713T110000Z
DTEND:20250713T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250713T090000Z
DTEND:20250713T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250713T110000Z
DTEND:20250713T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization forms (1NF\, 2NF\, 3NF\, BCNF)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250713T090000Z
DTEND:20250713T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250713T110000Z
DTEND:20250713T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and Concurrency Control
DTSTART:20250713T090000Z
DTEND:20250713T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250713T110000Z
DTEND:20250713T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConcurrency control mechanisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport Layer
DTSTART:20250713T090000Z
DTEND:20250713T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application Layer
DTSTART:20250713T110000Z
DTEND:20250713T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCongestion control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250714T090000Z
DTEND:20250714T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250714T110000Z
DTEND:20250714T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250714T090000Z
DTEND:20250714T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250714T110000Z
DTEND:20250714T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250714T090000Z
DTEND:20250714T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250714T110000Z
DTEND:20250714T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250714T090000Z
DTEND:20250714T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of Layering and Switching
DTSTART:20250714T110000Z
DTEND:20250714T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250714T090000Z
DTEND:20250714T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250714T110000Z
DTEND:20250714T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization forms (1NF\, 2NF\, 3NF\, BCNF)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250714T090000Z
DTEND:20250714T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250714T110000Z
DTEND:20250714T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and Concurrency Control
DTSTART:20250714T090000Z
DTEND:20250714T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250714T110000Z
DTEND:20250714T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConcurrency control mechanisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport Layer
DTSTART:20250714T090000Z
DTEND:20250714T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application Layer
DTSTART:20250714T110000Z
DTEND:20250714T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCongestion control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250715T090000Z
DTEND:20250715T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250715T110000Z
DTEND:20250715T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250715T090000Z
DTEND:20250715T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250715T110000Z
DTEND:20250715T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250715T090000Z
DTEND:20250715T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250715T110000Z
DTEND:20250715T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250715T090000Z
DTEND:20250715T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of Layering and Switching
DTSTART:20250715T110000Z
DTEND:20250715T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250715T090000Z
DTEND:20250715T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250715T110000Z
DTEND:20250715T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization forms (1NF\, 2NF\, 3NF\, BCNF)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250715T090000Z
DTEND:20250715T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250715T110000Z
DTEND:20250715T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and Concurrency Control
DTSTART:20250715T090000Z
DTEND:20250715T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250715T110000Z
DTEND:20250715T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConcurrency control mechanisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport Layer
DTSTART:20250715T090000Z
DTEND:20250715T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application Layer
DTSTART:20250715T110000Z
DTEND:20250715T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCongestion control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Lexical Analysis
DTSTART:20250716T090000Z
DTEND:20250716T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRegular expressions
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: System Calls and Processes
DTSTART:20250716T110000Z
DTEND:20250716T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFinite automata
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Parsing
DTSTART:20250716T090000Z
DTEND:20250716T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nContext-free grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Threads and Inter-process Communication
DTSTART:20250716T110000Z
DTEND:20250716T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nParse trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design: Syntax-Directed Translation
DTSTART:20250716T090000Z
DTEND:20250716T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nAttribute grammars
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System: Concurrency and Synchronization
DTSTART:20250716T110000Z
DTEND:20250716T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nSyntax trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: ER-Model and Relational Model
DTSTART:20250716T090000Z
DTEND:20250716T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nEntity-relationship diagrams
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Concept of Layering and Switching
DTSTART:20250716T110000Z
DTEND:20250716T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRelational algebra
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Integrity Constraints and Normal Forms
DTSTART:20250716T090000Z
DTEND:20250716T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFunctional dependencies
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Data Link Layer
DTSTART:20250716T110000Z
DTEND:20250716T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nNormalization forms (1NF\, 2NF\, 3NF\, BCNF)
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: File Organization and Indexing
DTSTART:20250716T090000Z
DTEND:20250716T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB-trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Routing Protocols
DTSTART:20250716T110000Z
DTEND:20250716T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nB+ trees
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Databases: Transactions and Concurrency Control
DTSTART:20250716T090000Z
DTEND:20250716T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nACID properties
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Network Layer
DTSTART:20250716T110000Z
DTEND:20250716T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nConcurrency control mechanisms
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Transport Layer
DTSTART:20250716T090000Z
DTEND:20250716T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nFlow control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Computer Networks: Application Layer
DTSTART:20250716T110000Z
DTEND:20250716T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nCongestion control
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Compiler Design
DTSTART:20250717T090000Z
DTEND:20250717T110000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nLexical analysis\, parsing\, syntax-directed tr
 anslation
END:VEVENT
BEGIN:VEVENT
SUMMARY:Study: Operating System
DTSTART:20250717T110000Z
DTEND:20250717T130000Z
DTSTAMP:20250411T231938Z
DESCRIPTION:Key Concepts:\nRuntime environments
END:VEVENT
END:VCALENDAR
