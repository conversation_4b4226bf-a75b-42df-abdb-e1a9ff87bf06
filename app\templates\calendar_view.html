{% extends "base.html" %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Exam Information Header -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
                <h2 class="text-2xl font-bold text-indigo-600">{{ exam_type }}</h2>
                <p class="text-gray-600">GATE CSE 2025</p>
            </div>
            <div class="text-center">
                <h3 class="text-xl font-semibold">Exam Date</h3>
                <p class="text-gray-600">{{ exam_date }}</p>
            </div>
            <div class="text-center">
                <h3 class="text-xl font-semibold">Days Remaining</h3>
                <p class="text-3xl font-bold text-indigo-600">{{ days_until_exam }}</p>
            </div>
        </div>
    </div>

    <!-- Calendar Navigation -->
    <div class="flex justify-between items-center mb-6">
        <button id="prev-month" class="text-indigo-600 hover:text-indigo-800 flex items-center">
            <svg class="h-5 w-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            Previous
        </button>
        <h2 id="current-month" class="text-2xl font-bold">{{ current_month }}</h2>
        <button id="next-month" class="text-indigo-600 hover:text-indigo-800 flex items-center">
            Next
            <svg class="h-5 w-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </button>
    </div>

    <!-- Calendar Grid -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <!-- Days of Week Header -->
        <div class="grid grid-cols-7 gap-px bg-gray-200 border-b">
            {% for day in ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'] %}
            <div class="text-center py-2 bg-gray-50">
                <span class="text-sm font-semibold">{{ day }}</span>
            </div>
            {% endfor %}
        </div>

        <!-- Calendar Days -->
        <div class="grid grid-cols-7 gap-px bg-gray-200">
            {% for week in calendar_weeks %}
                {% for day in week %}
                <div class="min-h-[120px] bg-white p-2 {% if day.today %}bg-indigo-50{% endif %}">
                    <div class="flex justify-between items-start">
                        <span class="text-sm {% if day.today %}font-bold text-indigo-600{% endif %}">{{ day.date }}</span>
                        {% if day.has_content %}
                        <span class="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded-full">{{ day.topic_count }} topics</span>
                        {% endif %}
                    </div>
                    
                    {% if day.content %}
                    <div class="mt-2">
                        {% for topic in day.content.topics[:2] %}
                        <div class="text-xs text-gray-600 truncate mb-1">{{ topic }}</div>
                        {% endfor %}
                        {% if day.content.topics|length > 2 %}
                        <div class="text-xs text-indigo-600 cursor-pointer hover:text-indigo-800">+ {{ day.content.topics|length - 2 }} more</div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            {% endfor %}
        </div>
    </div>

    <!-- Daily Content Modal -->
    <div id="content-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-date"></h3>
                <div class="mt-2 px-7 py-3">
                    <div id="modal-content"></div>
                </div>
                <div class="items-center px-4 py-3">
                    <button id="close-modal" class="px-4 py-2 bg-indigo-600 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
const calendar = {
    currentDate: new Date(),
    
    init() {
        this.updateCalendar();
        this.bindEvents();
    },

    bindEvents() {
        document.getElementById('prev-month').addEventListener('click', () => this.changeMonth(-1));
        document.getElementById('next-month').addEventListener('click', () => this.changeMonth(1));
        
        // Modal events
        document.getElementById('close-modal').addEventListener('click', this.closeModal);
    },

    changeMonth(delta) {
        this.currentDate.setMonth(this.currentDate.getMonth() + delta);
        this.updateCalendar();
    },

    updateCalendar() {
        const month = this.currentDate.toLocaleString('default', { month: 'long' });
        const year = this.currentDate.getFullYear();
        document.getElementById('current-month').textContent = `${month} ${year}`;
        
        // Here you would typically make an AJAX call to fetch the updated calendar data
        // and update the calendar grid
    },

    showModal(date, content) {
        const modal = document.getElementById('content-modal');
        const modalDate = document.getElementById('modal-date');
        const modalContent = document.getElementById('modal-content');

        modalDate.textContent = date;
        modalContent.innerHTML = this.formatModalContent(content);
        modal.classList.remove('hidden');
    },

    closeModal() {
        document.getElementById('content-modal').classList.add('hidden');
    },

    formatModalContent(content) {
        return `
            <div class="space-y-4">
                <div>
                    <h4 class="font-semibold text-sm text-gray-700">Topics</h4>
                    <ul class="mt-1 space-y-1">
                        ${content.topics.map(topic => `
                            <li class="text-sm text-gray-600">${topic}</li>
                        `).join('')}
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-sm text-gray-700">Key Concepts</h4>
                    <ul class="mt-1 space-y-1">
                        ${content.key_concepts.map(concept => `
                            <li class="text-sm text-gray-600">${concept}</li>
                        `).join('')}
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-sm text-gray-700">Time Allocation</h4>
                    <div class="mt-1">
                        ${Object.entries(content.time_allocation).map(([topic, hours]) => `
                            <div class="text-sm text-gray-600">${topic}: ${hours} hours</div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }
};

document.addEventListener('DOMContentLoaded', () => calendar.init());
</script>
{% endblock %}