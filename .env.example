# Application settings
APP_NAME=Educational Content Analysis System
DEBUG=True
SECRET_KEY=your_secret_key_here

# API Keys
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/credentials.json
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_API_KEY=your-google-api-key
OPENAI_API_KEY=your-openai-api-key

# File paths
INPUT_FOLDER=data/input
PROCESSED_FOLDER=data/processed
OUTPUT_FOLDER=data/output

# Database settings
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/education_content

# Web server settings
HOST=0.0.0.0
PORT=8000
