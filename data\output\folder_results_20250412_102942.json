[{"file_path": "data\\temp\\upload_4dc1adfe\\@vtucode.in-21CS71-module-1-pdf.pdf", "file_name": "@vtucode.in-21CS71-module-1-pdf.pdf", "extracted_text": "```text\n## Analysis of Big Data Analytics Notes\n\nThis document provides a comprehensive overview of Big Data Analytics, covering its need, characteristics, types, architecture, processing methods, tools, and applications.\n\n**Module 1: Introduction to Big Data Analytics**\n\n**1. Need for Big Data:**\n\n* The increasing volume, velocity, variety, and veracity of data generated by modern technologies have overwhelmed traditional data processing systems.\n* Challenges include storage, processing, analysis of diverse data formats, increasing complexity, and the need for rapid analysis and utilization.\n* The shift from megabytes to petabytes of data necessitates new approaches to data management and analysis.\n\n**(Figure: Data Usage and Growth)** -  Illustrates the evolution of Big Data, showing increasing size and complexity, with a growing proportion of unstructured data.  As data size grows from Megabytes to Petabytes, the data moves from simple structured types to complex and unstructured types.\n\n**2. Data:**\n\n* Definitions of data: Information (facts or statistics) used for calculations, computer programs, or presented in various formats (numbers, letters, observations).\n* Web Data: Data on web servers (text, images, videos, etc.) accessed by clients and used in internet applications.\n\n**3. Data Classification:**\n\n* **Structured Data:** Conforms to data schemas and models (e.g., tables). Enables easy manipulation (insert, delete, update, append), indexing, scalability, and security.\n* **Semi-structured Data:** Contains tags/markers for semantic separation (e.g., XML, JSON).  Doesn't conform to formal data models.\n* **Multi-structured Data:**  Combines structured, semi-structured, and unstructured data formats (e.g., streaming data, sensor data). Found in non-transactional systems.\n* **Unstructured Data:** Lacks predefined data features (e.g., text files, CSV). Relationships and schemas need to be established separately.\n\n**4. Big Data:**\n\n* Definitions:\n    * High-volume, high-velocity, high-variety information asset requiring new processing forms.\n    * Data too large or complex for traditional processing.\n    * Data whose manipulation and management present significant logistical challenges.\n    * Data exceeding the capacity of typical database software.\n\n**5. Big Data Characteristics (4Vs + Veracity):**\n\n* **Volume:** Amount/quantity of data.\n* **Velocity:** Speed of data generation and processing.\n* **Variety:** Different forms and formats of data from multiple sources.\n* **Veracity:** Quality and accuracy of captured data.\n\n**6. Big Data Types:**\n\n* Social networks and web data.\n* Transactions and business process data.\n* Customer master data.\n* Machine-generated data.\n* Human-generated data.\n\n**(Table: Big Data Classification)** - Categorizes Big Data based on traditional and Big Data sources, formats, storage structures, and processing rates.\n\n**7. Scalability and Parallel Processing:**\n\n* Big Data processing requires intensive computations and hundreds of nodes.\n* **Convergence of Data Environments and Analytics:** Scaling up (vertical – increasing system resources) and scaling out (horizontal – increasing number of systems).\n* **Analytics Scalability:** Vertical scalability increases system resources; horizontal scalability adds more systems to distribute the workload.\n* **Massively Parallel Processing (MPP):** Utilizing multiple computers/CPUs for parallel processing.  Tasks can be parallelized at thread, CPU, or computer level.\n* **Distributed Computing Model:** Using cloud/grid/clusters for processing and analyzing large datasets on interconnected nodes.\n* **Cloud Computing:** Internet-based computing providing shared resources on demand.  Enables parallel and distributed computing.  Three service models: IaaS, PaaS, and SaaS.\n* **Grid Computing:** Distributed computing where interconnected computers work together on a common task.  Suitable for data-intensive storage.\n* **Cluster Computing:** Group of networked computers working together on a single task, often used for load balancing.\n\n**8. Designing Data Architecture:**\n\n* **Data Architecture Design:** Logical/physical layout of how data is stored, accessed, and managed.  Consists of five logical layers:\n    1. Identification of data sources.\n    2. Data acquisition, ingestion, and pre-processing.\n    3. Data storage.\n    4. Data processing.\n    5. Data consumption.\n\n**9. Data Quality, Pre-processing:**\n\n* **Data Quality:** Enables accurate analysis, decisions, and knowledge discovery (5 Rs: relevancy, recency, range, robustness, reliability).\n* **Data Integrity:** Maintaining consistency and accuracy of data.\n* **Data Noise:** Meaningless information affecting data quality.\n* **Outliers:** Data points outside the expected range.\n* **Missing Values:** Data absent from the dataset.\n* **Duplicate Values:** Repeated data entries.\n* **Data Pre-processing:** Essential step before data mining/analytics, involving:\n    * Handling outliers, inconsistencies.\n    * Filtering unreliable/irrelevant data.\n    * Data cleaning, editing, reduction, wrangling, validation, transformation.\n    * Extract, Load, Transform (ELT) processing.\n\n**10. Big Data Platform:**\n\n* Supports large datasets and high-velocity, variety, and veracity data. Requires significant resources (MPPs, cloud, parallel processing).\n* Provides tools/services for storage, processing, analytics, environment management, application integration, and traditional/Big Data techniques.\n\n\n**Module 2: Hadoop and its Ecosystem**\n\n**(This module focuses on the Hadoop ecosystem, its architecture, and core components.)**\n\n**1. Introduction to Hadoop:**\n\n* Open-source framework written in Java for distributed processing of large datasets.\n* Scalable from single server to thousands of machines.\n* Uses a centralized computing model.\n\n**2. Big Data Store Model:**\n\n* Data stored in blocks distributed across nodes within racks in a cluster.\n* Data blocks are replicated for fault tolerance.\n\n**3. Big Data Programming Model:**\n\n* Application jobs and tasks scheduled on the servers storing the data.\n\n**4. Hadoop and its Ecosystem:**\n\n* Developed by Doug Cutting and Michael J. Cafarelle.\n* Core components:\n    * Hadoop Common (libraries and utilities).\n    * HDFS (distributed file system).\n    * MapReduce v1 (programming model).\n    * YARN (resource management).\n    * MapReduce v2 (YARN-based parallel processing).\n\n**5. Features of Hadoop:**\n\n* Fault-tolerant, scalable, flexible, modular.\n* Robust HDFS design.\n* Handles 3V characteristics of Big Data.\n* Distributed cluster computing with data locality.\n* Hardware fault-tolerant.\n* Open-source framework.\n* Java and Linux based.\n\n**6. Hadoop Ecosystem Components:**\n\n* Application Layer (ETL, analytics, ML).\n* Application Support Layer (Pig, Hive, Sqoop, Ambari, Chukwa).\n* MapReduce (job scheduling and execution).\n* HDFS (data storage).\n\n**7. Hadoop Distributed File System (HDFS):**\n\n* Stores data in blocks distributed across clusters, racks, and DataNodes.\n* Replicates blocks for fault tolerance.\n* Features: create, append, delete, rename files; write once, read many; large file sizes.\n\n**8. MapReduce Framework and Programming Model:**\n\n* Distributes jobs and aggregates results.\n* Two functions: job distribution and result aggregation.\n* JobTracker manages job execution.\n* Two processes: Mapper and Reducer.\n\n**9. Hadoop YARN:**\n\n* Resource management platform.\n* Manages computer resources and schedules sub-tasks.\n\n**10. Hadoop Ecosystem Tools:**\n\n* **ZooKeeper:** Centralized repository for distributed applications (concurrency control, configuration management, failure recovery).\n* **Oozie:** Workflow scheduler for Hadoop jobs.\n* **Sqoop:** Data transfer tool between Hadoop and RDBMS.\n* **Flume:**  Collects, aggregates, and transfers streaming data into HDFS.\n* **HBase:** NoSQL database built on Hadoop.\n\n**(The document then details the functionalities and usages of Pig, Hive, Sqoop, Flume, Oozie, and HBase with examples.)**\n\n**(The document also covers applications of Big Data in Marketing & Sales, Fraud Detection, Risk Management, Healthcare, Medicine, and Advertising.)**\n\n\nThis structured summary provides a concise overview of the key concepts and topics discussed in the provided document. Each section can be further expanded upon for more detailed analysis.\n```", "processed_at": "2025-04-12T10:29:42.579671", "chunk_ids": [], "chunks_count": 0}, {"file_path": "data\\temp\\upload_4dc1adfe\\@vtucode.in-21CS71-module-2-pdf.pdf", "file_name": "@vtucode.in-21CS71-module-2-pdf.pdf", "extracted_text": "```text\n## Analysis of Big Data Analytics Notes\n\nThis document provides a comprehensive overview of Big Data Analytics, covering its need, characteristics, types, architecture, processing methods, tools, and applications.\n\n**Module 1: Introduction to Big Data Analytics**\n\n**1. Need for Big Data:**\n\n* The increasing volume, velocity, variety, and veracity of data generated by modern technologies have overwhelmed traditional data processing systems.\n* Challenges include storage, processing, analysis of diverse data formats, increasing complexity, and the need for rapid analysis and utilization.\n* The shift from megabytes to petabytes of data necessitates new approaches to data management and analysis.\n\n**(Figure: Data Usage and Growth)** -  Illustrates the evolution of Big Data, showing increasing size and complexity, with a growing proportion of unstructured data.  As data size grows from Megabytes to Petabytes, the data moves from simple structured types to complex and unstructured types.\n\n**2. Data:**\n\n* Definitions of data: Information (facts or statistics) used for calculations, computer programs, or presented in various formats (numbers, letters, observations).\n* Web Data: Data on web servers (text, images, videos, etc.) accessed by clients and used in internet applications.\n\n**3. Data Classification:**\n\n* **Structured Data:** Conforms to data schemas and models (e.g., tables). Enables easy manipulation (insert, delete, update, append), indexing, scalability, and security.\n* **Semi-structured Data:** Contains tags/markers for semantic separation (e.g., XML, JSON).  Doesn't conform to formal data models.\n* **Multi-structured Data:**  Combines structured, semi-structured, and unstructured data formats (e.g., streaming data, sensor data). Found in non-transactional systems.\n* **Unstructured Data:** Lacks predefined data features (e.g., text files, CSV). Relationships and schemas need to be established separately.\n\n**4. Big Data:**\n\n* Definitions:\n    * High-volume, high-velocity, high-variety information asset requiring new processing forms.\n    * Data too large or complex for traditional processing.\n    * Data whose manipulation and management present significant logistical challenges.\n    * Data exceeding the capacity of typical database software.\n\n**5. Big Data Characteristics (4Vs + Veracity):**\n\n* **Volume:** Amount/quantity of data.\n* **Velocity:** Speed of data generation and processing.\n* **Variety:** Different forms and formats of data from multiple sources.\n* **Veracity:** Quality and accuracy of captured data.\n\n**6. Big Data Types:**\n\n* Social networks and web data.\n* Transactions and business process data.\n* Customer master data.\n* Machine-generated data.\n* Human-generated data.\n\n**(Table: Big Data Classification)** - Categorizes Big Data based on traditional and Big Data sources, formats, storage structures, and processing rates.\n\n**7. Scalability and Parallel Processing:**\n\n* Big Data processing requires intensive computations and hundreds of nodes.\n* **Convergence of Data Environments and Analytics:** Scaling up (vertical – increasing system resources) and scaling out (horizontal – increasing number of systems).\n* **Analytics Scalability:** Vertical scalability increases system resources; horizontal scalability adds more systems to distribute the workload.\n* **Massively Parallel Processing (MPP):** Utilizing multiple computers/CPUs for parallel processing.  Tasks can be parallelized at thread, CPU, or computer level.\n* **Distributed Computing Model:** Using cloud/grid/clusters for processing and analyzing large datasets on interconnected nodes.\n* **Cloud Computing:** Internet-based computing providing shared resources on demand.  Enables parallel and distributed computing.  Three service models: IaaS, PaaS, and SaaS.\n* **Grid Computing:** Distributed computing where interconnected computers work together on a common task.  Suitable for data-intensive storage.\n* **Cluster Computing:** Group of networked computers working together on a single task, often used for load balancing.\n\n**8. Designing Data Architecture:**\n\n* **Data Architecture Design:** Logical/physical layout of how data is stored, accessed, and managed.  Consists of five logical layers:\n    1. Identification of data sources.\n    2. Data acquisition, ingestion, and pre-processing.\n    3. Data storage.\n    4. Data processing.\n    5. Data consumption.\n\n**9. Data Quality, Pre-processing:**\n\n* **Data Quality:** Enables accurate analysis, decisions, and knowledge discovery (5 Rs: relevancy, recency, range, robustness, reliability).\n* **Data Integrity:** Maintaining consistency and accuracy of data.\n* **Data Noise:** Meaningless information affecting data quality.\n* **Outliers:** Data points outside the expected range.\n* **Missing Values:** Data absent from the dataset.\n* **Duplicate Values:** Repeated data entries.\n* **Data Pre-processing:** Essential step before data mining/analytics, involving:\n    * Handling outliers, inconsistencies.\n    * Filtering unreliable/irrelevant data.\n    * Data cleaning, editing, reduction, wrangling, validation, transformation.\n    * Extract, Load, Transform (ELT) processing.\n\n**10. Big Data Platform:**\n\n* Supports large datasets and high-velocity, variety, and veracity data. Requires significant resources (MPPs, cloud, parallel processing).\n* Provides tools/services for storage, processing, analytics, environment management, application integration, and traditional/Big Data techniques.\n\n\n**Module 2: Hadoop and its Ecosystem**\n\n**(This module focuses on the Hadoop ecosystem, its architecture, and core components.)**\n\n**1. Introduction to Hadoop:**\n\n* Open-source framework written in Java for distributed processing of large datasets.\n* Scalable from single server to thousands of machines.\n* Uses a centralized computing model.\n\n**2. Big Data Store Model:**\n\n* Data stored in blocks distributed across nodes within racks in a cluster.\n* Data blocks are replicated for fault tolerance.\n\n**3. Big Data Programming Model:**\n\n* Application jobs and tasks scheduled on the servers storing the data.\n\n**4. Hadoop and its Ecosystem:**\n\n* Developed by Doug Cutting and Michael J. Cafarelle.\n* Core components:\n    * Hadoop Common (libraries and utilities).\n    * HDFS (distributed file system).\n    * MapReduce v1 (programming model).\n    * YARN (resource management).\n    * MapReduce v2 (YARN-based parallel processing).\n\n**5. Features of Hadoop:**\n\n* Fault-tolerant, scalable, flexible, modular.\n* Robust HDFS design.\n* Handles 3V characteristics of Big Data.\n* Distributed cluster computing with data locality.\n* Hardware fault-tolerant.\n* Open-source framework.\n* Java and Linux based.\n\n**6. Hadoop Ecosystem Components:**\n\n* Application Layer (ETL, analytics, ML).\n* Application Support Layer (Pig, Hive, Sqoop, Ambari, Chukwa).\n* MapReduce (job scheduling and execution).\n* HDFS (data storage).\n\n**7. Hadoop Distributed File System (HDFS):**\n\n* Stores data in blocks distributed across clusters, racks, and DataNodes.\n* Replicates blocks for fault tolerance.\n* Features: create, append, delete, rename files; write once, read many; large file sizes.\n\n**8. MapReduce Framework and Programming Model:**\n\n* Distributes jobs and aggregates results.\n* Two functions: job distribution and result aggregation.\n* JobTracker manages job execution.\n* Two processes: Mapper and Reducer.\n\n**9. Hadoop YARN:**\n\n* Resource management platform.\n* Manages computer resources and schedules sub-tasks.\n\n**10. Hadoop Ecosystem Tools:**\n\n* **ZooKeeper:** Centralized repository for distributed applications (concurrency control, configuration management, failure recovery).\n* **Oozie:** Workflow scheduler for Hadoop jobs.\n* **Sqoop:** Data transfer tool between Hadoop and RDBMS.\n* **Flume:**  Collects, aggregates, and transfers streaming data into HDFS.\n* **HBase:** NoSQL database built on Hadoop.\n\n**(The document then details the functionalities and usages of Pig, Hive, Sqoop, Flume, Oozie, and HBase with examples.)**\n\n**(The document also covers applications of Big Data in Marketing & Sales, Fraud Detection, Risk Management, Healthcare, Medicine, and Advertising.)**\n\n\nThis structured summary provides a concise overview of the key concepts and topics discussed in the provided document. Each section can be further expanded upon for more detailed analysis.\n```", "processed_at": "2025-04-12T10:29:42.579671", "chunk_ids": [], "chunks_count": 0}]