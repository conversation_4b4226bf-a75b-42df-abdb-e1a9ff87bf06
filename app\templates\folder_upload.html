{% extends "base.html" %}

{% block title %}Folder Upload - Educational Content Analysis System{% endblock %}

{% block content %}
<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Upload Section -->
    <div class="md:col-span-2 bg-white rounded-lg shadow-md p-6">
        <h2 class="text-2xl font-bold mb-4">Upload Folder</h2>
        <div class="mb-6">
            <form id="upload-form" enctype="multipart/form-data">
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">Select Multiple Files (PDF or Images)</label>
                    <input type="file" id="file-input" name="files" multiple accept=".pdf,.jpg,.jpeg,.png,.webp" 
                           class="w-full p-2 border border-gray-300 rounded">
                </div>
                
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">Exam Type</label>
                    <select id="exam-type" name="exam_type" class="w-full p-2 border border-gray-300 rounded">
                        <option value="">-- Select Exam Type --</option>
                        <!-- Exam types will be populated dynamically -->
                    </select>
                </div>
                
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">Exam Date</label>
                    <input type="date" id="exam-date" name="exam_date" class="w-full p-2 border border-gray-300 rounded">
                </div>
                
                <div class="flex justify-between items-center">
                    <button type="submit" id="upload-button" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">
                        Upload and Process
                    </button>
                    <div id="upload-status" class="text-gray-600"></div>
                </div>
            </form>
        </div>
        
        <div id="results-container" class="hidden">
            <h3 class="text-xl font-bold mb-2">Processing Results</h3>
            <div id="results-content" class="border border-gray-200 rounded p-4 bg-gray-50 max-h-96 overflow-y-auto"></div>
        </div>
    </div>
    
    <!-- Exam Information -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-2xl font-bold mb-4">Exam Information</h2>
        
        <div id="exam-info" class="hidden">
            <div class="mb-4">
                <h3 class="text-lg font-semibold mb-2" id="exam-name">Exam Name</h3>
                <p class="text-gray-600" id="exam-description">Exam description will appear here.</p>
            </div>
            
            <div class="mb-4">
                <div class="flex justify-between">
                    <span class="text-gray-600">Preparation Time:</span>
                    <span class="font-semibold" id="exam-duration">6 months</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Your Exam Date:</span>
                    <span class="font-semibold" id="selected-date">Not selected</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Days Remaining:</span>
                    <span class="font-semibold" id="days-remaining">-</span>
                </div>
            </div>
        </div>
        
        <div id="no-exam-selected" class="text-center py-8">
            <div class="text-gray-400 text-5xl mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-500">No Exam Selected</h3>
            <p class="text-gray-400 mt-2">Please select an exam type to see information</p>
        </div>
    </div>
</div>

<!-- Motivational Quote -->
<div class="mt-6 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg shadow-md p-6 text-white">
    <div class="flex items-start">
        <div class="text-3xl mr-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
            </svg>
        </div>
        <div>
            <h3 class="text-xl font-bold mb-2">Today's Motivation</h3>
            <p id="motivational-quote" class="text-lg italic">"The harder you work for something, the greater you'll feel when you achieve it."</p>
        </div>
    </div>
</div>

<!-- Test Plans Section (will be shown after processing) -->
<div id="test-plans-section" class="mt-6 hidden">
    <h2 class="text-2xl font-bold mb-4">Generated Test Plans</h2>
    <div id="test-plans-container" class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Test plans will be populated dynamically -->
    </div>
</div>

<!-- Daily Content Section (will be shown after processing) -->
<div id="daily-content-section" class="mt-6 hidden">
    <h2 class="text-2xl font-bold mb-4">Your Daily Study Plan</h2>
    <div id="daily-content-container" class="bg-white rounded-lg shadow-md p-6">
        <!-- Daily content will be populated dynamically -->
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Fetch exam types on page load
    document.addEventListener('DOMContentLoaded', function() {
        fetchExamTypes();
        setDefaultExamDate();
        updateMotivationalQuote();
        
        // Add event listeners
        document.getElementById('exam-type').addEventListener('change', updateExamInfo);
        document.getElementById('exam-date').addEventListener('change', updateDaysRemaining);
        document.getElementById('upload-form').addEventListener('submit', uploadFiles);
    });
    
    // Fetch exam types from API
    async function fetchExamTypes() {
        try {
            const response = await fetch('/api/folder/exams');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const examTypes = await response.json();
            populateExamTypes(examTypes);
        } catch (error) {
            console.error('Error fetching exam types:', error);
        }
    }
    
    // Populate exam types dropdown
    function populateExamTypes(examTypes) {
        const examTypeSelect = document.getElementById('exam-type');
        
        examTypes.forEach(exam => {
            const option = document.createElement('option');
            option.value = exam.id;
            option.textContent = exam.name;
            option.dataset.description = exam.description;
            option.dataset.duration = exam.duration_months;
            examTypeSelect.appendChild(option);
        });
    }
    
    // Set default exam date (3 months from now)
    function setDefaultExamDate() {
        const examDateInput = document.getElementById('exam-date');
        const today = new Date();
        const threeMonthsLater = new Date(today);
        threeMonthsLater.setMonth(today.getMonth() + 3);
        
        const year = threeMonthsLater.getFullYear();
        const month = String(threeMonthsLater.getMonth() + 1).padStart(2, '0');
        const day = String(threeMonthsLater.getDate()).padStart(2, '0');
        
        examDateInput.value = `${year}-${month}-${day}`;
        updateDaysRemaining();
    }
    
    // Update exam information when exam type changes
    function updateExamInfo() {
        const examTypeSelect = document.getElementById('exam-type');
        const selectedOption = examTypeSelect.options[examTypeSelect.selectedIndex];
        
        const examInfo = document.getElementById('exam-info');
        const noExamSelected = document.getElementById('no-exam-selected');
        
        if (examTypeSelect.value) {
            // Show exam info
            examInfo.classList.remove('hidden');
            noExamSelected.classList.add('hidden');
            
            // Update exam details
            document.getElementById('exam-name').textContent = selectedOption.textContent;
            document.getElementById('exam-description').textContent = selectedOption.dataset.description;
            document.getElementById('exam-duration').textContent = `${selectedOption.dataset.duration} months`;
            
            updateDaysRemaining();
        } else {
            // Hide exam info
            examInfo.classList.add('hidden');
            noExamSelected.classList.remove('hidden');
        }
    }
    
    // Update days remaining until exam
    function updateDaysRemaining() {
        const examDateInput = document.getElementById('exam-date');
        const daysRemainingElement = document.getElementById('days-remaining');
        const selectedDateElement = document.getElementById('selected-date');
        
        if (examDateInput.value) {
            const examDate = new Date(examDateInput.value);
            const today = new Date();
            
            // Calculate days difference
            const diffTime = examDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            // Format date for display
            const options = { year: 'numeric', month: 'long', day: 'numeric' };
            const formattedDate = examDate.toLocaleDateString(undefined, options);
            
            // Update elements
            selectedDateElement.textContent = formattedDate;
            
            if (diffDays < 0) {
                daysRemainingElement.textContent = 'Exam date is in the past';
                daysRemainingElement.classList.add('text-red-600');
            } else {
                daysRemainingElement.textContent = `${diffDays} days`;
                daysRemainingElement.classList.remove('text-red-600');
            }
        } else {
            selectedDateElement.textContent = 'Not selected';
            daysRemainingElement.textContent = '-';
        }
    }
    
    // Upload files
    async function uploadFiles(e) {
        e.preventDefault();
        
        const fileInput = document.getElementById('file-input');
        const examType = document.getElementById('exam-type').value;
        const examDate = document.getElementById('exam-date').value;
        
        if (fileInput.files.length === 0) {
            alert('Please select at least one file to upload.');
            return;
        }
        
        if (!examType) {
            alert('Please select an exam type.');
            return;
        }
        
        if (!examDate) {
            alert('Please select an exam date.');
            return;
        }
        
        // Show loading state
        const uploadButton = document.getElementById('upload-button');
        const uploadStatus = document.getElementById('upload-status');
        
        uploadButton.disabled = true;
        uploadButton.innerHTML = '<div class="loading"></div> Processing...';
        uploadStatus.textContent = `Uploading ${fileInput.files.length} file(s)...`;
        
        // Create form data
        const formData = new FormData();
        for (let i = 0; i < fileInput.files.length; i++) {
            formData.append('files', fileInput.files[i]);
        }
        formData.append('exam_type', examType);
        formData.append('exam_date', examDate);
        
        try {
            const response = await fetch('/api/folder/upload', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            // Show results
            displayResults(data);
            
            // Reset form state
            uploadButton.disabled = false;
            uploadButton.textContent = 'Upload and Process';
            uploadStatus.textContent = `Successfully processed ${fileInput.files.length} file(s)`;
            
        } catch (error) {
            console.error('Error:', error);
            uploadButton.disabled = false;
            uploadButton.textContent = 'Upload and Process';
            uploadStatus.textContent = `Error: ${error.message}`;
        }
    }
    
    // Display processing results
    function displayResults(data) {
        const resultsContainer = document.getElementById('results-container');
        const resultsContent = document.getElementById('results-content');
        
        resultsContainer.classList.remove('hidden');
        
        if (data.status === 'success') {
            const result = data.result;
            
            let html = `
                <div class="mb-4">
                    <div class="text-green-600 font-semibold">${data.message}</div>
                    <div class="text-sm text-gray-600">Processed ${result.processed_files} files</div>
                </div>
            `;
            
            // Display test plans
            if (result.test_plans && result.test_plans.length > 0) {
                displayTestPlans(result.test_plans);
            }
            
            // Display daily content
            if (result.daily_content) {
                displayDailyContent(result.daily_content);
            }
            
            // Update motivational quote
            if (result.motivational_quotes && result.motivational_quotes.length > 0) {
                const randomQuote = result.motivational_quotes[Math.floor(Math.random() * result.motivational_quotes.length)];
                document.getElementById('motivational-quote').textContent = `"${randomQuote}"`;
            }
            
            resultsContent.innerHTML = html;
        } else {
            resultsContent.innerHTML = `
                <div class="text-red-600 font-semibold">${data.message}</div>
                <div class="text-sm text-gray-600">${data.result ? JSON.stringify(data.result) : 'No details available'}</div>
            `;
        }
    }
    
    // Display test plans
    function displayTestPlans(testPlans) {
        const testPlansSection = document.getElementById('test-plans-section');
        const testPlansContainer = document.getElementById('test-plans-container');
        
        testPlansSection.classList.remove('hidden');
        
        let html = '';
        
        testPlans.forEach((plan, index) => {
            html += `
                <div class="bg-white rounded-lg shadow-md p-4 hover-card">
                    <h3 class="text-lg font-semibold mb-2">${plan.title || `Test Plan ${index + 1}`}</h3>
                    <p class="text-gray-600 mb-3">${plan.description || 'Generated test plan'}</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">${plan.questions ? `${Array.isArray(plan.questions) ? plan.questions.length : '10'} questions` : ''}</span>
                        <button class="text-indigo-600 hover:text-indigo-800 text-sm font-medium view-test-plan" data-index="${index}">
                            View Plan
                        </button>
                    </div>
                </div>
            `;
        });
        
        testPlansContainer.innerHTML = html;
        
        // Add event listeners to view buttons
        document.querySelectorAll('.view-test-plan').forEach(button => {
            button.addEventListener('click', function() {
                const index = this.getAttribute('data-index');
                alert(`Viewing test plan ${parseInt(index) + 1}... (This would show the full test plan in a real app)`);
            });
        });
    }
    
    // Display daily content
    function displayDailyContent(dailyContent) {
        const dailyContentSection = document.getElementById('daily-content-section');
        const dailyContentContainer = document.getElementById('daily-content-container');
        
        dailyContentSection.classList.remove('hidden');
        
        let html = '';
        
        if (typeof dailyContent === 'object' && dailyContent.plan) {
            // If it's a simple object with a plan property
            html = `<div class="whitespace-pre-line">${dailyContent.plan}</div>`;
        } else if (Array.isArray(dailyContent)) {
            // If it's an array of daily plans
            html = '<div class="space-y-4">';
            
            dailyContent.forEach((day, index) => {
                html += `
                    <div class="border-b pb-4 ${index > 0 ? 'pt-4' : ''}">
                        <h3 class="text-lg font-semibold mb-2">Day ${index + 1}</h3>
                        <div class="whitespace-pre-line">${typeof day === 'string' ? day : JSON.stringify(day, null, 2)}</div>
                    </div>
                `;
            });
            
            html += '</div>';
        } else if (typeof dailyContent === 'object') {
            // If it's a complex object with daily plans
            html = '<div class="space-y-4">';
            
            // Try to find days property
            const days = dailyContent.days || dailyContent.daily_plans || Object.keys(dailyContent).filter(key => key.includes('day') || key.includes('Day'));
            
            if (days && days.length > 0) {
                days.forEach((day, index) => {
                    const dayContent = dailyContent[day] || day;
                    html += `
                        <div class="border-b pb-4 ${index > 0 ? 'pt-4' : ''}">
                            <h3 class="text-lg font-semibold mb-2">${typeof day === 'string' ? day : `Day ${index + 1}`}</h3>
                            <div class="whitespace-pre-line">${typeof dayContent === 'string' ? dayContent : JSON.stringify(dayContent, null, 2)}</div>
                        </div>
                    `;
                });
            } else {
                // Fallback to displaying the whole object
                html = `<pre class="text-sm overflow-x-auto">${JSON.stringify(dailyContent, null, 2)}</pre>`;
            }
            
            html += '</div>';
        } else {
            // Fallback for any other format
            html = `<div class="whitespace-pre-line">${dailyContent}</div>`;
        }
        
        dailyContentContainer.innerHTML = html;
    }
    
    // Update motivational quote
    function updateMotivationalQuote() {
        const quotes = [
            "The harder you work for something, the greater you'll feel when you achieve it.",
            "Success is the sum of small efforts, repeated day in and day out.",
            "Don't wish it were easier; wish you were better.",
            "The expert in anything was once a beginner.",
            "The only way to do great work is to love what you do.",
            "Believe you can and you're halfway there.",
            "It always seems impossible until it's done.",
            "Your time is limited, don't waste it living someone else's life.",
            "The future belongs to those who believe in the beauty of their dreams.",
            "The best way to predict the future is to create it."
        ];
        
        const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
        document.getElementById('motivational-quote').textContent = `"${randomQuote}"`;
    }
</script>
{% endblock %}
