[{"file_path": "data\\temp\\upload_cc9c07f9\\@vtucode.in-21CS71-module-1-pdf.pdf", "file_name": "@vtucode.in-21CS71-module-1-pdf.pdf", "extracted_text": "## Analysis of Big Data Analytics Notes\n\nThis document provides a comprehensive overview of Big Data Analytics, covering its need, characteristics, types, architecture, processing methods, associated tools, and applications.\n\n**Module 1: Introduction to Big Data Analytics**\n\n**1. Need for Big Data**\n\n* The exponential growth of data in volume, variety, and velocity has overwhelmed traditional data processing systems.\n* Challenges include storage, processing, analysis of diverse data formats, increasing complexity, and the need for rapid processing and analysis.\n\n**2. Data**\n\n* Definitions of data emphasize its role as information, often factual or statistical, used for calculations or analysis, and its ability to be stored and used by computer programs.\n* Web Data: Data residing on web servers in various formats (text, images, videos, etc.) accessed by users through client software.\n\n**3. Data Classification**\n\n* **Structured Data:** Adheres to predefined schemas and models (e.g., tables), enabling efficient data manipulation and retrieval. (15-20% of data)\n* **Semi-structured Data:** Contains tags or markers to separate semantic elements (e.g., XML, JSON), but lacks formal data model association.\n* **Multi-structured Data:**  Comprises a mix of structured, semi-structured, and unstructured data formats, often found in non-transactional systems.\n* **Unstructured Data:**  Lacks predefined structure (e.g., text files, CSV), requiring separate schema definition for analysis.\n\n**4. Big Data**\n\n* Defined as high-volume, high-velocity, and/or high-variety information requiring new processing methods for enhanced decision-making and insight discovery.\n* It poses challenges for traditional data processing applications due to its size and complexity.\n\n**5. Big Data Characteristics (4Vs + Veracity)**\n\n* **Volume:** The sheer quantity of data.\n* **Velocity:** The speed of data generation and processing.\n* **Variety:** Different data formats and sources.\n* **Veracity:** The quality and trustworthiness of data.\n\n**6. Big Data Types**\n\n* Social media and web data\n* Transaction and business process data\n* Customer master data\n* Machine-generated data\n* Human-generated data\n\n\n**7. Big Data Classification Table**\n\n| Basis of Classification | Examples |\n|---|---|\n| Data sources (traditional) | Records, RDBMS, distributed databases, data warehouses |\n| Data formats (traditional) | Structured and semi-structured |\n| Big Data sources | Distributed file systems, NoSQL databases, sensor data, web data |\n| Big Data formats | Unstructured, semi-structured, and multi-structured |\n| Data Stores structure | Web, enterprise, or cloud servers; data warehouses |\n| Processing data rates | Batch, near-time, real-time, streaming |\n\n\n**8. Data Usages**\n\n* Human interaction, business processes, knowledge discovery, enterprise applications.\n\n**9. Scalability and Parallel Processing**\n\n* Big Data processing requires intensive computations and parallel processing across numerous computing nodes.\n* **Vertical Scalability (Scaling Up):** Increasing resources of a single system.\n* **Horizontal Scalability (Scaling Out):** Adding more systems to distribute the workload.\n* **Massively Parallel Processing (MPP):** Utilizing multiple computers or CPUs for parallel task execution.\n* **Distributed Computing Model:** Employing cloud, grid, or clusters of interconnected nodes for processing large datasets.\n* **Cloud Computing:** Utilizing shared processing resources and data on demand over the internet.\n    * **IaaS (Infrastructure as a Service):** Access to hardware resources.\n    * **PaaS (Platform as a Service):** Runtime environment for application development.\n    * **SaaS (Software as a Service):**  Software applications provided as a service.\n* **Grid Computing:**  Connecting computers from different locations to achieve a common task, suitable for data-intensive storage.\n* **Cluster Computing:** Connecting computers via a network to work together, primarily used for load balancing.\n* **Volunteer Computing:** Utilizing computing resources provided by volunteers for distributed computing projects.\n\n**10. Designing Data Architecture**\n\n* Big Data architecture defines the logical and physical layout for data storage, access, and management.\n* **Five-layer approach:**\n    1. Identification of data sources.\n    2. Data acquisition, ingestion, pre-processing, and transformation.\n    3. Data storage (files, servers, clusters, cloud).\n    4. Data processing.\n    5. Data consumption (applications, BI, data mining, etc.).\n\n**11. Data Quality**\n\n* High-quality data is crucial for reliable analysis and decision-making.\n* **Five R's of Data Quality:** Relevancy, Recency, Range, Robustness, Reliability.\n* **Data Integrity:** Maintaining consistency and accuracy of data.\n* **Data Noise:**  Meaningless information within data.\n* **Outliers:** Data points outside the expected range.\n* **Missing Values:**  Absent data points.\n* **Duplicate Values:**  Repeated data points.\n\n**12. Data Pre-processing**\n\n* Essential for data mining and analytics, involves:\n    * Handling outliers, inconsistencies, and missing/duplicate values.\n    * Filtering irrelevant information.\n    * Data cleaning, editing, reduction, and wrangling.\n    * Data validation, transformation, and transcoding.\n    * ETL (Extract, Transform, Load) processing.\n\n**13. Big Data Platform**\n\n* Supports large datasets and high-velocity data.\n* Provides tools for storage, processing, analytics, environment management, and integration with other systems.\n\n**14. Hadoop**\n\n* Open-source framework for distributed processing of large datasets.\n* **Core Components:**\n    * Hadoop Common: Libraries and utilities.\n    * HDFS: Distributed file system.\n    * MapReduce: Programming model for parallel processing.\n    * YARN: Resource management platform.\n\n**15. Hadoop Ecosystem Tools**\n\n* **ZooKeeper:** Centralized repository for distributed applications.\n* **Oozie:** Workflow scheduler for Hadoop jobs.\n* **Sqoop:** Data transfer tool between Hadoop and RDBMS.\n* **Flume:**  Data collection, aggregation, and transfer tool.\n* **Pig:** High-level scripting language for MapReduce.\n* **Hive:** Data warehouse infrastructure for data summarization and querying.\n* **HBase:**  NoSQL database.\n\n**16. Applications and Case Studies**\n\n* **Marketing and Sales:** Customer value analytics, personalized advertising, fraud detection.\n* **Healthcare:** Value-based care, fraud detection, patient monitoring.\n* **Medicine:** Predictive modeling, research, healthcare system transformation.\n* **Advertising:** Real-time analytics, personalized targeting.\n\n**17. Big Data Risks**\n\n* Erroneous, inaccurate, or irrelevant data leading to flawed analytics.\n* Need for robust risk management procedures.\n\n**18. HDFS User Commands**\n\n* Basic commands for interacting with HDFS (e.g., `hdfs dfs -ls`, `hdfs dfs -put`, `hdfs dfs -get`, `hdfs dfs -rm`).\n\n\nThis structured summary provides a clear and organized overview of the key concepts and topics covered in the provided document. It extracts the important information and presents it in a readable and accessible format. It also includes the table, which helps to summarize some of the core concepts related to big data.  It focuses on the core ideas and provides a good basis for further study or review.", "processed_at": "2025-04-12T10:36:05.702517", "chunk_ids": [], "chunks_count": 0}, {"file_path": "data\\temp\\upload_cc9c07f9\\@vtucode.in-21CS71-module-2-pdf.pdf", "file_name": "@vtucode.in-21CS71-module-2-pdf.pdf", "extracted_text": "## Analysis of Big Data Analytics Notes\n\nThis document provides a comprehensive overview of Big Data Analytics, covering its need, characteristics, types, architecture, processing methods, associated tools, and applications.\n\n**Module 1: Introduction to Big Data Analytics**\n\n**1. Need for Big Data**\n\n* The exponential growth of data in volume, variety, and velocity has overwhelmed traditional data processing systems.\n* Challenges include storage, processing, analysis of diverse data formats, increasing complexity, and the need for rapid processing and analysis.\n\n**2. Data**\n\n* Definitions of data emphasize its role as information, often factual or statistical, used for calculations or analysis, and its ability to be stored and used by computer programs.\n* Web Data: Data residing on web servers in various formats (text, images, videos, etc.) accessed by users through client software.\n\n**3. Data Classification**\n\n* **Structured Data:** Adheres to predefined schemas and models (e.g., tables), enabling efficient data manipulation and retrieval. (15-20% of data)\n* **Semi-structured Data:** Contains tags or markers to separate semantic elements (e.g., XML, JSON), but lacks formal data model association.\n* **Multi-structured Data:**  Comprises a mix of structured, semi-structured, and unstructured data formats, often found in non-transactional systems.\n* **Unstructured Data:**  Lacks predefined structure (e.g., text files, CSV), requiring separate schema definition for analysis.\n\n**4. Big Data**\n\n* Defined as high-volume, high-velocity, and/or high-variety information requiring new processing methods for enhanced decision-making and insight discovery.\n* It poses challenges for traditional data processing applications due to its size and complexity.\n\n**5. Big Data Characteristics (4Vs + Veracity)**\n\n* **Volume:** The sheer quantity of data.\n* **Velocity:** The speed of data generation and processing.\n* **Variety:** Different data formats and sources.\n* **Veracity:** The quality and trustworthiness of data.\n\n**6. Big Data Types**\n\n* Social media and web data\n* Transaction and business process data\n* Customer master data\n* Machine-generated data\n* Human-generated data\n\n\n**7. Big Data Classification Table**\n\n| Basis of Classification | Examples |\n|---|---|\n| Data sources (traditional) | Records, RDBMS, distributed databases, data warehouses |\n| Data formats (traditional) | Structured and semi-structured |\n| Big Data sources | Distributed file systems, NoSQL databases, sensor data, web data |\n| Big Data formats | Unstructured, semi-structured, and multi-structured |\n| Data Stores structure | Web, enterprise, or cloud servers; data warehouses |\n| Processing data rates | Batch, near-time, real-time, streaming |\n\n\n**8. Data Usages**\n\n* Human interaction, business processes, knowledge discovery, enterprise applications.\n\n**9. Scalability and Parallel Processing**\n\n* Big Data processing requires intensive computations and parallel processing across numerous computing nodes.\n* **Vertical Scalability (Scaling Up):** Increasing resources of a single system.\n* **Horizontal Scalability (Scaling Out):** Adding more systems to distribute the workload.\n* **Massively Parallel Processing (MPP):** Utilizing multiple computers or CPUs for parallel task execution.\n* **Distributed Computing Model:** Employing cloud, grid, or clusters of interconnected nodes for processing large datasets.\n* **Cloud Computing:** Utilizing shared processing resources and data on demand over the internet.\n    * **IaaS (Infrastructure as a Service):** Access to hardware resources.\n    * **PaaS (Platform as a Service):** Runtime environment for application development.\n    * **SaaS (Software as a Service):**  Software applications provided as a service.\n* **Grid Computing:**  Connecting computers from different locations to achieve a common task, suitable for data-intensive storage.\n* **Cluster Computing:** Connecting computers via a network to work together, primarily used for load balancing.\n* **Volunteer Computing:** Utilizing computing resources provided by volunteers for distributed computing projects.\n\n**10. Designing Data Architecture**\n\n* Big Data architecture defines the logical and physical layout for data storage, access, and management.\n* **Five-layer approach:**\n    1. Identification of data sources.\n    2. Data acquisition, ingestion, pre-processing, and transformation.\n    3. Data storage (files, servers, clusters, cloud).\n    4. Data processing.\n    5. Data consumption (applications, BI, data mining, etc.).\n\n**11. Data Quality**\n\n* High-quality data is crucial for reliable analysis and decision-making.\n* **Five R's of Data Quality:** Relevancy, Recency, Range, Robustness, Reliability.\n* **Data Integrity:** Maintaining consistency and accuracy of data.\n* **Data Noise:**  Meaningless information within data.\n* **Outliers:** Data points outside the expected range.\n* **Missing Values:**  Absent data points.\n* **Duplicate Values:**  Repeated data points.\n\n**12. Data Pre-processing**\n\n* Essential for data mining and analytics, involves:\n    * Handling outliers, inconsistencies, and missing/duplicate values.\n    * Filtering irrelevant information.\n    * Data cleaning, editing, reduction, and wrangling.\n    * Data validation, transformation, and transcoding.\n    * ETL (Extract, Transform, Load) processing.\n\n**13. Big Data Platform**\n\n* Supports large datasets and high-velocity data.\n* Provides tools for storage, processing, analytics, environment management, and integration with other systems.\n\n**14. Hadoop**\n\n* Open-source framework for distributed processing of large datasets.\n* **Core Components:**\n    * Hadoop Common: Libraries and utilities.\n    * HDFS: Distributed file system.\n    * MapReduce: Programming model for parallel processing.\n    * YARN: Resource management platform.\n\n**15. Hadoop Ecosystem Tools**\n\n* **ZooKeeper:** Centralized repository for distributed applications.\n* **Oozie:** Workflow scheduler for Hadoop jobs.\n* **Sqoop:** Data transfer tool between Hadoop and RDBMS.\n* **Flume:**  Data collection, aggregation, and transfer tool.\n* **Pig:** High-level scripting language for MapReduce.\n* **Hive:** Data warehouse infrastructure for data summarization and querying.\n* **HBase:**  NoSQL database.\n\n**16. Applications and Case Studies**\n\n* **Marketing and Sales:** Customer value analytics, personalized advertising, fraud detection.\n* **Healthcare:** Value-based care, fraud detection, patient monitoring.\n* **Medicine:** Predictive modeling, research, healthcare system transformation.\n* **Advertising:** Real-time analytics, personalized targeting.\n\n**17. Big Data Risks**\n\n* Erroneous, inaccurate, or irrelevant data leading to flawed analytics.\n* Need for robust risk management procedures.\n\n**18. HDFS User Commands**\n\n* Basic commands for interacting with HDFS (e.g., `hdfs dfs -ls`, `hdfs dfs -put`, `hdfs dfs -get`, `hdfs dfs -rm`).\n\n\nThis structured summary provides a clear and organized overview of the key concepts and topics covered in the provided document. It extracts the important information and presents it in a readable and accessible format. It also includes the table, which helps to summarize some of the core concepts related to big data.  It focuses on the core ideas and provides a good basis for further study or review.", "processed_at": "2025-04-12T10:36:05.702517", "chunk_ids": [], "chunks_count": 0}]