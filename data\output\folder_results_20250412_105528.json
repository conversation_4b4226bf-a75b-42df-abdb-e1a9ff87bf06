[{"file_path": "data\\temp\\upload_ad701024\\@vtucode.in-21CS71-module-1-pdf.pdf", "file_name": "@vtucode.in-21CS71-module-1-pdf.pdf", "extracted_text": "## Analysis of Big Data Analytics Notes\n\nThis document provides a comprehensive overview of Big Data Analytics, covering its definition, characteristics, architecture, processing methods, tools, and applications.\n\n### Module 1: Introduction to Big Data Analytics\n\n**1. Need for Big Data**\n\n* The increasing volume, variety, velocity, and complexity of data generated by modern technologies necessitate new approaches to data storage, processing, and analysis. Traditional systems struggle to handle these challenges.\n\n**2. Data**\n\n* Definitions: Information that can be analyzed, used for calculations, stored by a computer program, or presented in various forms (numbers, letters, etc.).\n* Web Data: Data residing on web servers, encompassing various formats like text, images, videos, and accessed through client-server interactions.\n\n**3. Data Types**\n\n* **Structured Data:** Conforms to predefined schemas (e.g., tables), enabling efficient data manipulation and retrieval.\n* **Semi-structured Data:** Contains tags or markers defining hierarchies (e.g., XML, JSON), but lacks formal data models.\n* **Multi-structured Data:** Combines structured, semi-structured, and unstructured data, often found in non-transactional systems.\n* **Unstructured Data:** Lacks predefined structure (e.g., text files, CSV), requiring separate schema establishment for analysis.\n\n**4. Big Data**\n\n* Definition: High-volume, high-velocity, and/or high-variety information requiring new processing forms for enhanced decision-making and insight discovery.  It exceeds the capacity of traditional data processing applications.\n\n**5. Big Data Characteristics (4Vs + Veracity)**\n\n* **Volume:** The sheer quantity of data generated.\n* **Velocity:** The speed at which data is generated and processed.\n* **Variety:** Different formats and types of data.\n* **Veracity:** The quality and trustworthiness of data.\n\n**6. Big Data Types**\n\n* Social Media and Web Data\n* Transactional and Business Process Data\n* Customer Master Data\n* Machine-generated Data\n* Human-generated Data\n\n\n**7. Big Data Classification Table**\n\n| Basis of Classification | Examples |\n|---|---|\n| Data Sources (Traditional) | Records, RDBMS, Data Warehouses, Server Data |\n| Data Formats (Traditional) | Structured and Semi-structured |\n| Big Data Sources | Distributed File Systems, NoSQL Databases, Sensor Data, Web Data |\n| Big Data Formats | Unstructured, Semi-structured, Multi-structured |\n| Data Stores Structure | Web Servers, Data Warehouses, Row-oriented, Column-oriented, Graph Databases |\n| Processing Data Rates | Batch, Near-time, Real-time, Streaming |\n\n**8. Scalability and Parallel Processing**\n\n* **Vertical Scalability (Scaling Up):** Increasing resources of a single system.\n* **Horizontal Scalability (Scaling Out):** Adding more systems to distribute workload.\n* **Massively Parallel Processing (MPP):** Utilizing multiple computers or CPUs for processing large programs.\n* **Distributed Computing Model:** Using cloud, grid, or clusters of interconnected nodes for processing and analyzing large datasets.\n* **Cloud Computing:** Internet-based computing providing shared resources on demand (IaaS, PaaS, SaaS).\n\n\n**9. Grid and Cluster Computing**\n\n* **Grid Computing:** Connecting computers from various locations to achieve a common task, suitable for data-intensive storage.\n* **Cluster Computing:** Connecting computers in a network to work together, primarily for load balancing.\n* **Volunteer Computing:** Utilizing computing resources from volunteers for distributed computing projects.\n\n\n**10. Designing Data Architecture**\n\n* **Big Data Architecture:** Logical and physical structure for storing, accessing, and managing Big Data.\n* **Five-layer Architecture:**\n    1. Data Source Identification\n    2. Data Ingestion and Acquisition\n    3. Data Storage\n    4. Data Processing\n    5. Data Consumption\n\n**11. Data Quality**\n\n* **5 Rs of Data Quality:** Relevancy, Recency, Range, Robustness, Reliability.\n* **Data Integrity:** Maintaining consistency and accuracy of data.\n* **Data Noise:** Meaningless information affecting data quality.\n* **Outliers:** Data points outside the expected range.\n* **Missing Values:** Gaps in the dataset.\n* **Duplicate Values:** Repeated data entries.\n\n**12. Data Pre-processing**\n\n* Essential steps before data mining and analytics, including cleaning, editing, reduction, wrangling, and transformation.\n\n**13. Big Data Platform**\n\n* Supports large datasets and high-velocity data processing, providing tools for storage, processing, analytics, and environment management.\n\n**14. Hadoop**\n\n* Open-source framework for distributed processing of large datasets.\n* **Ecosystem:** HDFS, MapReduce, YARN, ZooKeeper, Pig, Hive, Sqoop, Flume, Oozie, HBase.\n\n**15. Big Data Applications**\n\n* Marketing and Sales (Customer Value Analytics, Fraud Detection)\n* Healthcare (Value-based care, Fraud detection, Patient monitoring)\n* Medicine (Predictive modeling, Research)\n* Advertising (Hyper-localized advertising)\n\n\n### Module 2: Hadoop and its Ecosystem\n\nThis module delves deeper into the Hadoop ecosystem and its components. It provides a more technical explanation of the concepts introduced in Module 1.  The content is organized by Hadoop components and tools, detailing their functionalities and usage. The OCR also includes code examples for Hive, HDFS commands, and visual representations of Hadoop cluster architecture and data flow.\n\n\n\nThis structured analysis provides a clear and comprehensive overview of the provided document, highlighting key concepts and organizing information into a more readable format. The inclusion of the table and list formatting helps to present information effectively and enhances the overall understanding of the material.  It should be noted that the OCR has some minor errors and inconsistencies, especially in the second module, which might require manual correction.", "processed_at": "2025-04-12T10:55:28.386605", "chunk_ids": [], "chunks_count": 0}, {"file_path": "data\\temp\\upload_ad701024\\@vtucode.in-21CS71-module-2-pdf.pdf", "file_name": "@vtucode.in-21CS71-module-2-pdf.pdf", "extracted_text": "## Analysis of Big Data Analytics Notes\n\nThis document provides a comprehensive overview of Big Data Analytics, covering its definition, characteristics, architecture, processing methods, tools, and applications.\n\n### Module 1: Introduction to Big Data Analytics\n\n**1. Need for Big Data**\n\n* The increasing volume, variety, velocity, and complexity of data generated by modern technologies necessitate new approaches to data storage, processing, and analysis. Traditional systems struggle to handle these challenges.\n\n**2. Data**\n\n* Definitions: Information that can be analyzed, used for calculations, stored by a computer program, or presented in various forms (numbers, letters, etc.).\n* Web Data: Data residing on web servers, encompassing various formats like text, images, videos, and accessed through client-server interactions.\n\n**3. Data Types**\n\n* **Structured Data:** Conforms to predefined schemas (e.g., tables), enabling efficient data manipulation and retrieval.\n* **Semi-structured Data:** Contains tags or markers defining hierarchies (e.g., XML, JSON), but lacks formal data models.\n* **Multi-structured Data:** Combines structured, semi-structured, and unstructured data, often found in non-transactional systems.\n* **Unstructured Data:** Lacks predefined structure (e.g., text files, CSV), requiring separate schema establishment for analysis.\n\n**4. Big Data**\n\n* Definition: High-volume, high-velocity, and/or high-variety information requiring new processing forms for enhanced decision-making and insight discovery.  It exceeds the capacity of traditional data processing applications.\n\n**5. Big Data Characteristics (4Vs + Veracity)**\n\n* **Volume:** The sheer quantity of data generated.\n* **Velocity:** The speed at which data is generated and processed.\n* **Variety:** Different formats and types of data.\n* **Veracity:** The quality and trustworthiness of data.\n\n**6. Big Data Types**\n\n* Social Media and Web Data\n* Transactional and Business Process Data\n* Customer Master Data\n* Machine-generated Data\n* Human-generated Data\n\n\n**7. Big Data Classification Table**\n\n| Basis of Classification | Examples |\n|---|---|\n| Data Sources (Traditional) | Records, RDBMS, Data Warehouses, Server Data |\n| Data Formats (Traditional) | Structured and Semi-structured |\n| Big Data Sources | Distributed File Systems, NoSQL Databases, Sensor Data, Web Data |\n| Big Data Formats | Unstructured, Semi-structured, Multi-structured |\n| Data Stores Structure | Web Servers, Data Warehouses, Row-oriented, Column-oriented, Graph Databases |\n| Processing Data Rates | Batch, Near-time, Real-time, Streaming |\n\n**8. Scalability and Parallel Processing**\n\n* **Vertical Scalability (Scaling Up):** Increasing resources of a single system.\n* **Horizontal Scalability (Scaling Out):** Adding more systems to distribute workload.\n* **Massively Parallel Processing (MPP):** Utilizing multiple computers or CPUs for processing large programs.\n* **Distributed Computing Model:** Using cloud, grid, or clusters of interconnected nodes for processing and analyzing large datasets.\n* **Cloud Computing:** Internet-based computing providing shared resources on demand (IaaS, PaaS, SaaS).\n\n\n**9. Grid and Cluster Computing**\n\n* **Grid Computing:** Connecting computers from various locations to achieve a common task, suitable for data-intensive storage.\n* **Cluster Computing:** Connecting computers in a network to work together, primarily for load balancing.\n* **Volunteer Computing:** Utilizing computing resources from volunteers for distributed computing projects.\n\n\n**10. Designing Data Architecture**\n\n* **Big Data Architecture:** Logical and physical structure for storing, accessing, and managing Big Data.\n* **Five-layer Architecture:**\n    1. Data Source Identification\n    2. Data Ingestion and Acquisition\n    3. Data Storage\n    4. Data Processing\n    5. Data Consumption\n\n**11. Data Quality**\n\n* **5 Rs of Data Quality:** Relevancy, Recency, Range, Robustness, Reliability.\n* **Data Integrity:** Maintaining consistency and accuracy of data.\n* **Data Noise:** Meaningless information affecting data quality.\n* **Outliers:** Data points outside the expected range.\n* **Missing Values:** Gaps in the dataset.\n* **Duplicate Values:** Repeated data entries.\n\n**12. Data Pre-processing**\n\n* Essential steps before data mining and analytics, including cleaning, editing, reduction, wrangling, and transformation.\n\n**13. Big Data Platform**\n\n* Supports large datasets and high-velocity data processing, providing tools for storage, processing, analytics, and environment management.\n\n**14. Hadoop**\n\n* Open-source framework for distributed processing of large datasets.\n* **Ecosystem:** HDFS, MapReduce, YARN, ZooKeeper, Pig, Hive, Sqoop, Flume, Oozie, HBase.\n\n**15. Big Data Applications**\n\n* Marketing and Sales (Customer Value Analytics, Fraud Detection)\n* Healthcare (Value-based care, Fraud detection, Patient monitoring)\n* Medicine (Predictive modeling, Research)\n* Advertising (Hyper-localized advertising)\n\n\n### Module 2: Hadoop and its Ecosystem\n\nThis module delves deeper into the Hadoop ecosystem and its components. It provides a more technical explanation of the concepts introduced in Module 1.  The content is organized by Hadoop components and tools, detailing their functionalities and usage. The OCR also includes code examples for Hive, HDFS commands, and visual representations of Hadoop cluster architecture and data flow.\n\n\n\nThis structured analysis provides a clear and comprehensive overview of the provided document, highlighting key concepts and organizing information into a more readable format. The inclusion of the table and list formatting helps to present information effectively and enhances the overall understanding of the material.  It should be noted that the OCR has some minor errors and inconsistencies, especially in the second module, which might require manual correction.", "processed_at": "2025-04-12T10:55:28.386605", "chunk_ids": [], "chunks_count": 0}]