{% extends "base.html" %}

{% block title %}Dashboard - e-Gurukool{% endblock %}

{% block head %}
<style>
    .upload-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid rgba(99, 102, 241, 0.1);
        transition: all 0.3s ease;
    }

    .upload-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .stat-card {
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
    }

    .file-input-wrapper {
        position: relative;
        overflow: hidden;
        display: inline-block;
    }

    .file-input-wrapper input[type=file] {
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
        cursor: pointer;
    }

    .custom-file-input {
        display: inline-block;
        padding: 8px 16px;
        background: #f3f4f6;
        border: 2px dashed #d1d5db;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .custom-file-input:hover {
        background: #e5e7eb;
        border-color: #9ca3af;
    }

    .progress-bar {
        height: 4px;
        background: #e5e7eb;
        border-radius: 2px;
        overflow: hidden;
    }

    .progress-bar-fill {
        height: 100%;
        background: linear-gradient(90deg, #4f46e5, #7c3aed);
        transition: width 0.3s ease;
    }

    .dark .upload-card {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        border-color: rgba(99, 102, 241, 0.2);
    }

    .dark .custom-file-input {
        background: #374151;
        border-color: #4b5563;
    }

    .dark .custom-file-input:hover {
        background: #4b5563;
        border-color: #6b7280;
    }
</style>
{% endblock %}

{% block content %}
<!-- Motivational Quote -->
<div class="mt-6 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg shadow-md p-6 text-white" style="margin-bottom: :20px;">
    <div class="flex items-start">
        <div class="text-3xl mr-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
            </svg>
        </div>
        <div>
            <h3 class="text-xl font-bold mb-2">Today's Motivation</h3>
            <p id="motivational-quote" class="text-lg italic">"The harder you work for something, the greater you'll feel when you achieve it."</p>
        </div>
    </div>
</div>
<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Upload Section -->
    <div class="md:col-span-2 upload-card rounded-xl shadow-lg p-6">
        <h2 class="text-2xl font-bold mb-4 text-gray-800 dark:text-white">Upload Educational Content</h2>
        <div class="mb-6">
            <form id="upload-form" enctype="multipart/form-data" class="space-y-4">
                <div class="mb-4">
                    <label class="block text-gray-700 dark:text-gray-300 mb-2 font-medium">Select Multiple Files</label>
                    <div class="file-input-wrapper w-full">
                        <input type="file" id="file-input" name="files" multiple accept=".pdf,.jpg,.jpeg,.png,.webp"
                               class="w-full">
                        <div class="custom-file-input w-full text-center py-8">
                            <svg class="w-12 h-12 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                            </svg>
                            <p class="text-gray-600 dark:text-gray-400">Drag and drop files here or click to browse</p>
                            <p class="text-sm text-gray-500 dark:text-gray-500 mt-1">Supported formats: PDF, JPG, PNG, WEBP</p>
                        </div>
                    </div>
                    <div id="selected-files" class="mt-2 space-y-2"></div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-700 dark:text-gray-300 mb-2 font-medium">Exam Type</label>
                        <select id="exam-type" name="exam_type" 
                                class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">-- Select Exam Type --</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-gray-700 dark:text-gray-300 mb-2 font-medium">Exam Date</label>
                        <input type="date" id="exam-date" name="exam_date" 
                               class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                </div>

                <div class="flex justify-between items-center mt-6">
                    <button type="submit" id="upload-button" 
                            class="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"/>
                        </svg>
                        Upload and Process
                    </button>
                    <div id="upload-status" class="text-gray-600 dark:text-gray-400"></div>
                </div>

                <div id="upload-progress" class="hidden">
                    <div class="progress-bar">
                        <div id="progress-bar-fill" class="progress-bar-fill" style="width: 0%"></div>
                    </div>
                </div>
            </form>
        </div>

        <div id="results-container" class="hidden">
            <h3 class="text-xl font-bold mb-4 text-gray-800 dark:text-white">Processing Results</h3>
            <div id="results-content" class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800 max-h-96 overflow-y-auto"></div>
        </div>
    </div>

    <!-- Dashboard Summary -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h2 class="text-2xl font-bold mb-4 text-gray-800 dark:text-white">Dashboard</h2>

        <div id="exam-info" class="mb-6 hidden">
            <div class="bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-4">
                <h3 class="text-lg font-semibold mb-2 text-indigo-800 dark:text-indigo-200" id="exam-name">Exam Name</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-3" id="exam-description">Exam description will appear here.</p>

                <div class="grid grid-cols-2 gap-3 text-sm">
                    <div class="bg-white dark:bg-gray-700 p-2 rounded-lg">
                        <span class="text-gray-600 dark:text-gray-400">Preparation Time:</span>
                        <span class="font-semibold text-gray-800 dark:text-white" id="exam-duration">6 months</span>
                    </div>
                    <div class="bg-white dark:bg-gray-700 p-2 rounded-lg">
                        <span class="text-gray-600 dark:text-gray-400">Exam Date:</span>
                        <span class="font-semibold text-gray-800 dark:text-white" id="selected-date">Not selected</span>
                    </div>
                    <div class="bg-white dark:bg-gray-700 p-2 rounded-lg">
                        <span class="text-gray-600 dark:text-gray-400">Days Remaining:</span>
                        <span class="font-semibold text-gray-800 dark:text-white" id="days-remaining">-</span>
                    </div>
                </div>
            </div>
        </div>

        <div id="no-exam-selected" class="mb-6">
            <div class="text-center py-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <svg class="w-12 h-12 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                <p class="text-gray-500 dark:text-gray-400">Select an exam type to see information</p>
            </div>
        </div>

        <div class="mb-6">
            <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-white">Quick Stats</h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="stat-card bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <div id="files-processed" class="text-3xl font-bold text-blue-700 dark:text-blue-400">0</div>
                            <div class="text-sm text-blue-700 dark:text-blue-400">Files Processed</div>
                        </div>
                        <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-card bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <div id="tests-generated" class="text-3xl font-bold text-green-700 dark:text-green-400">0</div>
                            <div class="text-sm text-green-700 dark:text-green-400">Tests Generated</div>
                        </div>
                        <svg class="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-card bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <div id="questions-analyzed" class="text-3xl font-bold text-purple-700 dark:text-purple-400">0</div>
                            <div class="text-sm text-purple-700 dark:text-purple-400">Questions Analyzed</div>
                        </div>
                        <svg class="w-8 h-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-card bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <div id="topics-identified" class="text-3xl font-bold text-yellow-700 dark:text-yellow-400">0</div>
                            <div class="text-sm text-yellow-700 dark:text-yellow-400">Topics Identified</div>
                        </div>
                        <svg class="w-8 h-8 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <div>
            <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-white">Question Distribution</h3>
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <canvas id="question-chart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>



<!-- Test Plans Section (will be shown after processing) -->
<div id="test-plans-section" class="mt-6 hidden">
    <h2 class="text-2xl font-bold mb-4">Generated Test Plans</h2>
    <div id="test-plans-container" class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Test plans will be populated dynamically -->
    </div>
</div>

<!-- Note: Daily study plans are now shown in the Planner section -->

<!-- Recent Activity -->
<div class="mt-6 bg-white rounded-lg shadow-md p-6">
    <h2 class="text-2xl font-bold mb-4">Recent Activity</h2>
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                        Date
                    </th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                        Activity
                    </th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                        Details
                    </th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                        Status
                    </th>
                </tr>
            </thead>
            <tbody id="activity-table-body">
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200" colspan="4">
                        No recent activity
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // File input handling
    const fileInput = document.getElementById('file-input');
    const selectedFilesContainer = document.getElementById('selected-files');

    fileInput.addEventListener('change', function() {
        selectedFilesContainer.innerHTML = '';
        Array.from(this.files).forEach(file => {
            const fileElement = document.createElement('div');
            fileElement.className = 'flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg';
            fileElement.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <span class="text-sm text-gray-600 dark:text-gray-300">${file.name}</span>
                </div>
                <span class="text-xs text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</span>
            `;
            selectedFilesContainer.appendChild(fileElement);
        });
    });

    // Drag and drop handling
    const dropZone = document.querySelector('.custom-file-input');

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        dropZone.classList.add('border-indigo-500', 'bg-indigo-50');
    }

    function unhighlight(e) {
        dropZone.classList.remove('border-indigo-500', 'bg-indigo-50');
    }

    dropZone.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        fileInput.files = files;
        fileInput.dispatchEvent(new Event('change'));
    }

    // Progress bar handling
    function updateProgress(percent) {
        const progressBar = document.getElementById('progress-bar-fill');
        progressBar.style.width = `${percent}%`;
    }

    // Chart initialization
    const ctx = document.getElementById('question-chart').getContext('2d');
    const chart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Objective', 'Subjective', 'Practical'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: [
                    'rgba(79, 70, 229, 0.8)',
                    'rgba(124, 58, 237, 0.8)',
                    'rgba(37, 99, 235, 0.8)'
                ],
                borderColor: [
                    'rgba(79, 70, 229, 1)',
                    'rgba(124, 58, 237, 1)',
                    'rgba(37, 99, 235, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: window.matchMedia('(prefers-color-scheme: dark)').matches ? '#fff' : '#000'
                    }
                }
            }
        }
    });

    // Dark mode chart update
    document.addEventListener('darkModeChanged', (e) => {
        chart.options.plugins.legend.labels.color = e.detail.darkMode ? '#fff' : '#000';
        chart.update();
    });

    // Fetch exam types on page load
    document.addEventListener('DOMContentLoaded', function() {
        fetchExamTypes();
        setDefaultExamDate();
        updateMotivationalQuote();

        // Add event listeners
        document.getElementById('exam-type').addEventListener('change', updateExamInfo);
        document.getElementById('exam-date').addEventListener('change', updateDaysRemaining);
        document.getElementById('upload-form').addEventListener('submit', uploadFiles);
    });

    // Fetch exam types from API
    async function fetchExamTypes() {
        try {
            const response = await fetch('/api/folder/exams');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const examTypes = await response.json();
            populateExamTypes(examTypes);
        } catch (error) {
            console.error('Error fetching exam types:', error);
        }
    }

    // Populate exam types dropdown
    function populateExamTypes(examTypes) {
        const examTypeSelect = document.getElementById('exam-type');

        examTypes.forEach(exam => {
            const option = document.createElement('option');
            option.value = exam.id;
            option.textContent = exam.name;
            option.dataset.description = exam.description;
            option.dataset.duration = exam.duration_months;
            examTypeSelect.appendChild(option);
        });
    }

    // Set default exam date (3 months from now)
    function setDefaultExamDate() {
        const examDateInput = document.getElementById('exam-date');
        const today = new Date();
        const threeMonthsLater = new Date(today);
        threeMonthsLater.setMonth(today.getMonth() + 3);

        const year = threeMonthsLater.getFullYear();
        const month = String(threeMonthsLater.getMonth() + 1).padStart(2, '0');
        const day = String(threeMonthsLater.getDate()).padStart(2, '0');

        examDateInput.value = `${year}-${month}-${day}`;
        updateDaysRemaining();
    }

    // Update exam information when exam type changes
    function updateExamInfo() {
        const examTypeSelect = document.getElementById('exam-type');
        const selectedOption = examTypeSelect.options[examTypeSelect.selectedIndex];

        const examInfo = document.getElementById('exam-info');
        const noExamSelected = document.getElementById('no-exam-selected');

        if (examTypeSelect.value) {
            // Show exam info
            examInfo.classList.remove('hidden');
            noExamSelected.classList.add('hidden');

            // Update exam details
            document.getElementById('exam-name').textContent = selectedOption.textContent;
            document.getElementById('exam-description').textContent = selectedOption.dataset.description;
            document.getElementById('exam-duration').textContent = `${selectedOption.dataset.duration} months`;

            updateDaysRemaining();
        } else {
            // Hide exam info
            examInfo.classList.add('hidden');
            noExamSelected.classList.remove('hidden');
        }
    }

    // Update days remaining until exam
    function updateDaysRemaining() {
        const examDateInput = document.getElementById('exam-date');
        const daysRemainingElement = document.getElementById('days-remaining');
        const selectedDateElement = document.getElementById('selected-date');

        if (examDateInput.value) {
            const examDate = new Date(examDateInput.value);
            const today = new Date();

            // Calculate days difference
            const diffTime = examDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            // Format date for display
            const options = { year: 'numeric', month: 'long', day: 'numeric' };
            const formattedDate = examDate.toLocaleDateString(undefined, options);

            // Update elements
            selectedDateElement.textContent = formattedDate;

            if (diffDays < 0) {
                daysRemainingElement.textContent = 'Exam date is in the past';
                daysRemainingElement.classList.add('text-red-600');
            } else {
                daysRemainingElement.textContent = `${diffDays} days`;
                daysRemainingElement.classList.remove('text-red-600');
            }
        } else {
            selectedDateElement.textContent = 'Not selected';
            daysRemainingElement.textContent = '-';
        }
    }

    // Upload files
    async function uploadFiles(e) {
        e.preventDefault();

        const fileInput = document.getElementById('file-input');
        const examType = document.getElementById('exam-type').value;
        const examDate = document.getElementById('exam-date').value;

        if (fileInput.files.length === 0) {
            alert('Please select at least one file to upload.');
            return;
        }

        if (!examType) {
            alert('Please select an exam type.');
            return;
        }

        if (!examDate) {
            alert('Please select an exam date.');
            return;
        }

        // Show loading state
        const uploadButton = document.getElementById('upload-button');
        const uploadStatus = document.getElementById('upload-status');

        uploadButton.disabled = true;
        uploadButton.innerHTML = '<div class="loading"></div> Processing...';
        uploadStatus.textContent = `Uploading ${fileInput.files.length} file(s)...`;

        // Create form data
        const formData = new FormData();
        for (let i = 0; i < fileInput.files.length; i++) {
            formData.append('files', fileInput.files[i]);
        }
        formData.append('exam_type', examType);
        formData.append('exam_date', examDate);

        try {
            const response = await fetch('/api/folder/upload', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            // Show results
            displayResults(data);

            // Update dashboard stats
            updateDashboardStats(data.result);

            // Update activity table
            updateActivityTable(data);

            // Reset form state
            uploadButton.disabled = false;
            uploadButton.textContent = 'Upload and Process';
            uploadStatus.textContent = `Successfully processed ${fileInput.files.length} file(s)`;

        } catch (error) {
            console.error('Error:', error);
            uploadButton.disabled = false;
            uploadButton.textContent = 'Upload and Process';
            uploadStatus.textContent = `Error: ${error.message}`;
        }
    }

    // Display processing results
    function displayResults(data) {
        const resultsContainer = document.getElementById('results-container');
        const resultsContent = document.getElementById('results-content');

        resultsContainer.classList.remove('hidden');

        if (data.status === 'success') {
            const result = data.result;

            let html = `
                <div class="mb-4">
                    <div class="text-green-600 font-semibold">${data.message}</div>
                    <div class="text-sm text-gray-600">Processed ${result.processed_files} files</div>
                </div>
            `;

            // Display test plans
            if (result.test_plans && result.test_plans.length > 0) {
                displayTestPlans(result.test_plans);
            }

            // Note: Daily content is now handled in the planner section

            // Update motivational quote
            if (result.motivational_quotes && result.motivational_quotes.length > 0) {
                const randomQuote = result.motivational_quotes[Math.floor(Math.random() * result.motivational_quotes.length)];
                document.getElementById('motivational-quote').textContent = `"${randomQuote}"`;
            }

            resultsContent.innerHTML = html;
        } else {
            resultsContent.innerHTML = `
                <div class="text-red-600 font-semibold">${data.message}</div>
                <div class="text-sm text-gray-600">${data.result ? JSON.stringify(data.result) : 'No details available'}</div>
            `;
        }
    }

    // Update dashboard stats
    function updateDashboardStats(result) {
        if (!result) return;

        // Update files processed
        document.getElementById('files-processed').textContent = result.processed_files || 0;

        // Update tests generated
        const testCount = result.test_plans ? result.test_plans.length : 0;
        document.getElementById('tests-generated').textContent = testCount;

        // Update questions analyzed (estimate based on test plans)
        let questionCount = 0;
        if (result.test_plans) {
            result.test_plans.forEach(plan => {
                if (plan.questions) {
                    questionCount += Array.isArray(plan.questions) ? plan.questions.length : 10;
                }
            });
        }
        document.getElementById('questions-analyzed').textContent = questionCount;

        // Update topics identified
        let topicsCount = 0;
        if (result.daily_content && result.daily_content.topics) {
            topicsCount = result.daily_content.topics.length;
        }
        document.getElementById('topics-identified').textContent = topicsCount || 5; // Default to 5 if not available

        // Update chart with distribution data
        if (result.test_plans && result.test_plans.length > 0) {
            const firstPlan = result.test_plans[0];
            if (firstPlan.distribution) {
                const distribution = firstPlan.distribution;
                updateChart([
                    distribution.objective?.marks || 30,
                    distribution.subjective?.marks || 40,
                    distribution.practical?.marks || 30
                ]);
            } else {
                // Default distribution if not available
                updateChart([30, 40, 30]);
            }
        } else {
            // Default distribution if no test plans
            updateChart([30, 40, 30]);
        }
    }

    // Display test plans
    function displayTestPlans(testPlans) {
        const testPlansSection = document.getElementById('test-plans-section');
        const testPlansContainer = document.getElementById('test-plans-container');

        testPlansSection.classList.remove('hidden');

        let html = '';

        testPlans.forEach((plan, index) => {
            html += `
                <div class="bg-white rounded-lg shadow-md p-4 hover-card">
                    <h3 class="text-lg font-semibold mb-2">${plan.title || `Test Plan ${index + 1}`}</h3>
                    <p class="text-gray-600 mb-3">${plan.description || 'Generated test plan'}</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">${plan.questions ? `${Array.isArray(plan.questions) ? plan.questions.length : '10'} questions` : ''}</span>
                        <button class="text-indigo-600 hover:text-indigo-800 text-sm font-medium view-test-plan" data-index="${index}">
                            View Plan
                        </button>
                    </div>
                </div>
            `;
        });

        testPlansContainer.innerHTML = html;

        // Add event listeners to view buttons
        document.querySelectorAll('.view-test-plan').forEach(button => {
            button.addEventListener('click', function() {
                const index = this.getAttribute('data-index');
                alert(`Viewing test plan ${parseInt(index) + 1}... (This would show the full test plan in a real app)`);
            });
        });
    }

    // Note: Daily content display has been moved to the planner page

    // Update activity table
    function updateActivityTable(data) {
        const activityTableBody = document.getElementById('activity-table-body');

        if (!data || !data.result) {
            return;
        }

        const result = data.result;
        const date = new Date().toLocaleString();
        const statusClass = data.status === 'success' ? 'text-green-600' : 'text-red-600';

        let html = `
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">${date}</td>
                <td class="py-2 px-4 border-b border-gray-200">Folder Processing</td>
                <td class="py-2 px-4 border-b border-gray-200">${result.processed_files} files for ${result.exam_type}</td>
                <td class="py-2 px-4 border-b border-gray-200 ${statusClass}">${data.status}</td>
            </tr>
        `;

        activityTableBody.innerHTML = html;
    }

    // Update chart
    function updateChart(data) {
        chart.data.datasets[0].data = data;
        chart.update();
    }

    // Update motivational quote
    function updateMotivationalQuote() {
        const quotes = [
            "The harder you work for something, the greater you'll feel when you achieve it.",
            "Success is the sum of small efforts, repeated day in and day out.",
            "Don't wish it were easier; wish you were better.",
            "The expert in anything was once a beginner.",
            "The only way to do great work is to love what you do.",
            "Believe you can and you're halfway there.",
            "It always seems impossible until it's done.",
            "Your time is limited, don't waste it living someone else's life.",
            "The future belongs to those who believe in the beauty of their dreams.",
            "The best way to predict the future is to create it."
        ];

        const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
        document.getElementById('motivational-quote').textContent = `"${randomQuote}"`;
    }
</script>
{% endblock %}
