<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Study Plan Calendar</title>
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css' rel='stylesheet' />
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .export-btn {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        .export-btn:hover {
            background-color: #45a049;
        }
        #calendar {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Study Plan Calendar</h1>
            <a href="/export/ics" class="export-btn">Export to Calendar</a>
        </div>
        <div id="calendar"></div>
    </div>

    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js'></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var calendarEl = document.getElementById('calendar');
            var calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                events: function(info, successCallback, failureCallback) {
                    fetch('/api/study_plan')
                        .then(response => response.json())
                        .then(data => {
                            const events = [];
                            for (const [dayKey, dayData] of Object.entries(data.daily_plans)) {
                                const content = JSON.parse(dayData.content.split('```json')[1].split('```')[0]);
                                const dayNumber = dayKey.split('_')[1];
                                const topics = content[`day${dayNumber}`].topics;
                                
                                events.push({
                                    title: topics.join(', '),
                                    start: dayData.date,
                                    allDay: true,
                                    description: dayData.content
                                });
                            }
                            successCallback(events);
                        })
                        .catch(error => {
                            console.error('Error fetching study plan:', error);
                            failureCallback(error);
                        });
                },
                eventClick: function(info) {
                    alert('Topics: ' + info.event.title + '\n\nDetails: ' + info.event.extendedProps.description);
                }
            });
            calendar.render();
        });
    </script>
</body>
</html> 