[{"file_path": "data\\temp\\upload_e23aa758\\@vtucode.in-21CS71-module-2-pdf.pdf", "file_name": "@vtucode.in-21CS71-module-2-pdf.pdf", "extracted_text": "## Analysis of Module 2: Introduction to <PERSON><PERSON>\n\nThis document introduces Hadoop and its ecosystem, focusing on its architecture, components, and related tools.\n\n**1. Introduction to Hadoop**\n\n* **What is <PERSON><PERSON>?** An open-source framework written in Java for distributed processing of large datasets across clusters of computers.  It provides distributed storage and computation.  It's designed to scale from a single server to thousands of machines.\n\n* **Big Data Store Model:** Data is stored in a file system consisting of data blocks distributed across multiple nodes within racks within clusters. Data blocks are replicated for fault tolerance.\n\n* **Big Data Programming Model:** Application jobs and tasks are scheduled on the same servers that store the data, enabling data locality.\n\n* **Hadoop and its Ecosystem:** Initiated by Apache, named after a toy elephant.  Consists of data storage and parallel computation components.\n\n* **Hadoop Core Components:**\n    * **HDFS (Hadoop Distributed File System):** Java-based distributed file system.\n    * **MapReduce (v1 & v2):** Programming model for parallel processing. V2 uses YARN.\n    * **YARN (Yet Another Resource Negotiator):** Resource management for computing.\n    * **Common:** Libraries and utilities used by other modules.\n\n* **Features of Hadoop:**\n    * Fault-tolerant, scalable, flexible, and modular.\n    * Robust HDFS design with replication and data recovery.\n    * Distributed cluster computing with data locality.\n    * Hardware fault-tolerant due to data block replication.\n    * Open-source framework.\n    * Java and Linux based.\n\n* **Hadoop Ecosystem Components (Layered Architecture):**\n    1. **Distributed Storage Layer (HDFS):** Stores data in blocks across clusters, racks, and DataNodes.\n    2. **Resource Manager Layer (YARN):** Schedules and manages resources for applications.\n    3. **Processing Framework Layer (MapReduce):** Processes data using Mapper and Reducer.\n    4. **Application Support Layer (APIs):** Provides tools like Pig, Hive, Sqoop, Flume, Oozie, HBase, Mahout, Zookeeper.\n\n\n**2. Hadoop Distributed File System (HDFS)**\n\n* **Design Features:**\n    * Write-once/read-many model optimized for streaming reads.\n    * No random seeks or caching.\n    * Converged storage and processing on the same nodes.\n    * Multiple data copies for reliability.\n    * Specialized file system.\n\n* **Components:**\n    * **NameNode:** Master node managing metadata.\n    * **DataNodes:** Slave nodes storing data blocks.\n    * **Secondary NameNode:** Performs checkpoints but is not a failover node.\n\n* **HDFS User Commands:**  Includes `hdfs dfs -ls`, `-mkdir`, `-put`, `-get`, `-cp`, `-rm`.\n\n\n**3. MapReduce Framework**\n\n* **Functionality:** Distributes jobs and aggregates results.\n* **JobTracker:** Manages job execution, resource allocation, and task monitoring.\n* **Two Main Processes:**\n    * **Mapper:** Processes data and converts it into key-value pairs.\n    * **Reducer:** Aggregates the mapped data.\n* **Programming Model:** Can be written in Java, C++, Pipes, or Python. Input data is stored in HDFS.\n\n\n**4. Hadoop YARN**\n\n* **Function:** Resource management platform that manages schedules for running sub-tasks.\n* **Components:** Client, Resource Manager (RM), Node Manager (NM), Application Master (AM), and Containers.\n* **Resource Allocation and Scheduling:** Client sends requests to RM, which manages resources and allocates containers to AMs running on NMs.\n\n\n\n**5. Hadoop Ecosystem Tools**\n\n* **Zookeeper:** Centralized repository for distributed applications.  Provides name service, concurrency control, configuration management, and failure recovery.\n\n* **Oozie:** Workflow scheduler for Hadoop jobs, supporting various job types (workflow, coordinator, bundle).\n\n* **Sqoop:** Tool for efficient data transfer between Hadoop and relational databases. Supports import and export functionalities.\n\n* **Flume:** Distributed service for collecting, aggregating, and moving large amounts of streaming data into HDFS. Features robustness and fault tolerance. Consists of sources, channels, sinks and agents.\n\n* **Pig:** High-level scripting language (Pig Latin) for writing complex MapReduce transformations. Used for ETL, quick research, and data pipelines.\n\n* **Hive:** Data warehouse infrastructure built on Hadoop for data summarization, ad-hoc queries, and analysis using HiveQL (SQL-like language).\n\n* **HBase:** Distributed NoSQL database built on Hadoop providing Bigtable-like capabilities. Offers linear scalability, consistent reads/writes, automatic sharding, and failover support.\n\n\n**Example:**\n\nThe student data storage example illustrates HDFS concepts like data block distribution, replication, and cluster capacity planning.\n\n\n**Key Concepts:**\n\n* Distributed computing\n* Data locality\n* Fault tolerance\n* Scalability\n* MapReduce\n* HDFS\n* YARN\n\n\n**Formulas:**\n\n* Memory conversions (MB, GB, TB)\n\n\n**Important Points:**\n\n* Hadoop's architecture and components.\n* HDFS design principles.\n* MapReduce programming model.\n* Functionality of ecosystem tools.\n\n\nThis structured analysis provides a comprehensive overview of Module 2, covering the essential aspects of Hadoop and its ecosystem. The information is organized by topics and subtopics, making it easy to understand and navigate.  Tables from the original document have been integrated into the text for better readability.", "processed_at": "2025-04-12T09:31:31.526241", "chunk_ids": [], "chunks_count": 0}]