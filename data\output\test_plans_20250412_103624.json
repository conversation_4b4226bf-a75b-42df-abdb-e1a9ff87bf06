[{"title": "Test Plan 1 for gate_cse", "description": "Generated test plan", "questions": "```json\n[\n  {\n    \"title\": \"Big Data Fundamentals Test 1\",\n    \"description\": \"This test covers the basic concepts of Big Data, its characteristics, types, and challenges.\",\n    \"questions\": [\n      {\n        \"question\": \"What are the three Vs of Big Data?\",\n        \"answer\": \"Volume, Velocity, Variety\",\n        \"explanation\": \"These three characteristics define the core challenges of Big Data: the sheer amount of data, the speed at which it is generated and processed, and the different formats it comes in.\",\n        \"difficulty\": \"easy\",\n        \"topic\": \"Big Data Characteristics\"\n      },\n      {\n        \"question\": \"Which type of data lacks a predefined structure and requires schema definition for analysis?\",\n        \"answer\": \"Unstructured Data\",\n        \"explanation\": \"Unstructured data like text files and images don't adhere to a specific format, requiring schemas for analysis.\",\n        \"difficulty\": \"easy\",\n        \"topic\": \"Data Classification\"\n      },\n      {\n        \"question\": \"What is Veracity in the context of Big Data?\",\n        \"answer\": \"The quality and trustworthiness of data\",\n        \"explanation\": \"Veracity refers to the reliability and accuracy of the data, ensuring its suitability for analysis.\",\n        \"difficulty\": \"medium\",\n        \"topic\": \"Big Data Characteristics\"\n      },\n      {\n        \"question\": \"Give two examples of traditional data sources.\",\n        \"answer\": \"Records, RDBMS\",\n        \"explanation\": \"Records and RDBMS represent traditional structured data storage methods.\",\n        \"difficulty\": \"easy\",\n        \"topic\": \"Data Sources\"\n      },\n      {\n        \"question\": \"What is the difference between structured and semi-structured data?\",\n        \"answer\": \"Structured data adheres to a predefined schema, while semi-structured data uses tags to separate semantic elements but lacks a formal model.\",\n        \"explanation\": \"The key difference lies in the presence of a formal schema. Structured data conforms to a predefined model, while semi-structured data relies on tags for organization.\",\n        \"difficulty\": \"medium\",\n        \"topic\": \"Data Classification\"\n      },\n      {\n        \"question\": \"Name two Big Data formats.\",\n        \"answer\": \"Unstructured, Semi-structured\",\n        \"explanation\": \"Big Data often comes in unstructured formats like text and images, or semi-structured formats like JSON and XML.\",\n        \"difficulty\": \"easy\",\n        \"topic\": \"Big Data Formats\"\n      },\n      {\n        \"question\": \"What is the purpose of data pre-processing?\",\n        \"answer\": \"To prepare data for analysis by handling inconsistencies, missing values, and irrelevant information.\",\n        \"explanation\": \"Data pre-processing cleans and transforms data to improve the quality and reliability of subsequent analysis.\",\n        \"difficulty\": \"medium\",\n        \"topic\": \"Data Pre-processing\"\n      },\n      {\n        \"question\": \"Explain the difference between horizontal and vertical scalability.\",\n        \"answer\": \"Vertical scalability involves increasing resources of a single system, while horizontal scalability adds more systems to distribute the workload.\",\n        \"explanation\": \"Scaling up vs. scaling out represents different approaches to handling increased computational demands.\",\n        \"difficulty\": \"hard\",\n        \"topic\": \"Scalability\"\n      },\n      {\n        \"question\": \"What are the core components of Hadoop?\",\n        \"answer\": \"Hadoop Common, HDFS, MapReduce, YARN\",\n        \"explanation\": \"These components provide the foundation for distributed processing in Hadoop.\",\n        \"difficulty\": \"hard\",\n        \"topic\": \"Hadoop\"\n      },\n      {\n        \"question\": \"What is the role of Flume in the Hadoop ecosystem?\",\n        \"answer\": \"Data collection, aggregation, and transfer.\",\n        \"explanation\": \"Flume efficiently gathers and moves large volumes of data into Hadoop.\",\n        \"difficulty\": \"hard\",\n        \"topic\": \"Hadoop Ecosystem\"\n      }\n    ],\n    \"time_estimate\": \"60 minutes\"\n  },\n  {\n    \"title\": \"Big Data Technologies Test 2\",\n    \"description\": \"This test focuses on Hadoop, its ecosystem, and related technologies.\",\n    \"questions\": [\n        // Questions for test 2, similar structure as above. Focus on Hadoop ecosystem and related technologies.\n    ],\n    \"time_estimate\": \"60 minutes\"\n  },\n  {\n    \"title\": \"Big Data Applications and Challenges Test 3\",\n    \"description\": \"This test covers the applications, risks, and ethical considerations of Big Data.\",\n        \"questions\": [\n        // Questions for test 3, similar structure as above. Focus on applications, risks and ethical considerations.\n    ],\n    \"time_estimate\": \"60 minutes\"\n  }\n]\n```\n\nThis provides a starting point for the JSON. You'll need to fill in the questions for Test 2 and Test 3 using the provided content, following the same structure as Test 1.  Make sure the questions are diverse and cover the topics mentioned in the description of each test.  Adjust the difficulty level as needed. Remember to keep each JSON file concise to avoid exceeding token limits.  You can split it into multiple files if necessary."}]