{% extends "base.html" %}

{% block title %}Register - Educational Content Analysis System{% endblock %}

{% block content %}
<div class="flex justify-center items-center min-h-[calc(100vh-200px)]">
    <div class="bg-white rounded-lg shadow-md p-8 w-full max-w-md">
        <h2 class="text-2xl font-bold mb-6 text-center">Create an Account</h2>
        
        <div id="register-error" class="hidden mb-4 p-3 bg-red-100 text-red-700 rounded"></div>
        <div id="register-success" class="hidden mb-4 p-3 bg-green-100 text-green-700 rounded"></div>
        
        <form id="register-form" class="space-y-4">
            <div>
                <label for="username" class="block text-gray-700 mb-2">Username</label>
                <input type="text" id="username" name="username" class="w-full p-2 border border-gray-300 rounded" required>
            </div>
            
            <div>
                <label for="email" class="block text-gray-700 mb-2">Email</label>
                <input type="email" id="email" name="email" class="w-full p-2 border border-gray-300 rounded" required>
            </div>
            
            <div>
                <label for="full_name" class="block text-gray-700 mb-2">Full Name</label>
                <input type="text" id="full_name" name="full_name" class="w-full p-2 border border-gray-300 rounded" required>
            </div>
            
            <div>
                <label for="password" class="block text-gray-700 mb-2">Password</label>
                <input type="password" id="password" name="password" class="w-full p-2 border border-gray-300 rounded" required>
                <p class="text-xs text-gray-500 mt-1">Password must be at least 8 characters long</p>
            </div>
            
            <div>
                <label for="confirm_password" class="block text-gray-700 mb-2">Confirm Password</label>
                <input type="password" id="confirm_password" name="confirm_password" class="w-full p-2 border border-gray-300 rounded" required>
            </div>
            
            <div>
                <label for="role" class="block text-gray-700 mb-2">Account Type</label>
                <select id="role" name="role" class="w-full p-2 border border-gray-300 rounded" required>
                    <option value="">Select account type</option>
                    <option value="student">Student</option>
                    <option value="teacher">Teacher</option>
                </select>
            </div>
            
            <div class="flex justify-between items-center">
                <button type="submit" class="bg-indigo-600 text-white px-6 py-2 rounded hover:bg-indigo-700">
                    Register
                </button>
                <a href="/login" class="text-indigo-600 hover:text-indigo-800">Already have an account?</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.getElementById('register-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        const email = document.getElementById('email').value;
        const fullName = document.getElementById('full_name').value;
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        const role = document.getElementById('role').value;
        
        const errorDiv = document.getElementById('register-error');
        const successDiv = document.getElementById('register-success');
        
        // Reset messages
        errorDiv.classList.add('hidden');
        successDiv.classList.add('hidden');
        
        // Validate form
        if (password.length < 8) {
            errorDiv.textContent = 'Password must be at least 8 characters long';
            errorDiv.classList.remove('hidden');
            return;
        }
        
        if (password !== confirmPassword) {
            errorDiv.textContent = 'Passwords do not match';
            errorDiv.classList.remove('hidden');
            return;
        }
        
        if (!role) {
            errorDiv.textContent = 'Please select an account type';
            errorDiv.classList.remove('hidden');
            return;
        }
        
        try {
            const response = await fetch('/api/auth/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username,
                    email,
                    full_name: fullName,
                    password,
                    role
                })
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                errorDiv.textContent = data.error || 'Registration failed. Please try again.';
                errorDiv.classList.remove('hidden');
                return;
            }
            
            // Show success message
            successDiv.textContent = 'Registration successful! Redirecting to login...';
            successDiv.classList.remove('hidden');
            
            // Reset form
            document.getElementById('register-form').reset();
            
            // Redirect to login page after a delay
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
            
        } catch (error) {
            console.error('Error:', error);
            errorDiv.textContent = 'An error occurred. Please try again.';
            errorDiv.classList.remove('hidden');
        }
    });
</script>
{% endblock %}
