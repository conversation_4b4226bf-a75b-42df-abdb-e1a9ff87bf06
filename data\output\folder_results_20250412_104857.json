[{"file_path": "data\\temp\\upload_eb418dd9\\@vtucode.in-21CS71-module-1-pdf.pdf", "file_name": "@vtucode.in-21CS71-module-1-pdf.pdf", "extracted_text": "```\n## Analysis of Big Data Analytics Notes\n\nThis document provides an overview of Big Data Analytics, covering its need, characteristics, types, architecture, processing, tools, and applications.\n\n### Module 1: Introduction to Big Data Analytics\n\n**1. Need for Big Data:**\n\n* The increase in technology has resulted in massive data production and storage, moving from megabytes to petabytes.\n* Conventional systems struggle with the volume, variety, velocity, and complexity of this data.\n* Big data analytics addresses the need for quick processing, analysis, and utilization of this data.\n* Data growth is visualized as a triangle with increasing size and complexity leading to a higher proportion of unstructured data.\n\n**2. Data:**\n\n* Definitions:\n    * Information (facts or statistics) for analysis and calculations.\n    * Information stored and used by computer programs.\n    * Information represented in numbers, letters, or other forms.\n    * Information from observations, measurements, or facts.\n* Web Data: Data on web servers (text, images, videos, etc.) accessed by users (clients) through interactions like pulling, pushing, and posting data. Sources include websites, web services, emails, social media, etc.\n\n\n**3. Data Types:**\n\n* **Structured Data:** Conforms to schemas and models (e.g., tables). Enables operations like insert, delete, update, append, indexing, scaling, and encryption.\n* **Semi-structured Data:** Contains tags/markers that separate semantic elements and create hierarchies (e.g., XML, JSON). Does not conform to formal data models.\n* **Multi-structured Data:** Consists of multiple data formats (structured, semi-structured, unstructured) found in non-transactional systems.\n* **Unstructured Data:** Lacks predefined structure (e.g., text files, CSV). Relationships, schema, and features need to be established.\n\n**4. Big Data:**\n\n* Definition:  High-volume, high-velocity, and/or high-variety information asset requiring new processing forms for enhanced decision-making and insight discovery.\n* Other definitions highlight the challenges of processing and managing such large datasets with traditional tools.\n\n**5. Big Data Characteristics (4Vs):**\n\n* **Volume:**  The amount of data generated.\n* **Velocity:** The speed of data generation and processing.\n* **Variety:** Different forms and formats of data.\n* **Veracity:**  The quality and accuracy of data.\n\n**6. Big Data Types:**\n\n* Social media and web data\n* Transactional and business process data\n* Customer master data\n* Machine-generated data\n* Human-generated data\n\n**7. Big Data Classification Table:**\n\n| Basis of Classification | Examples |\n|---|---|\n| Data sources (traditional) | Records, RDBMS, data warehouses |\n| Data formats (traditional) | Structured, semi-structured |\n| Big Data sources | NoSQL databases, sensor data, web data |\n| Big Data formats | Unstructured, semi-structured, multi-structured |\n| Data Stores structure | Row-oriented, column-oriented, graph databases |\n| Processing data rates | Batch, near-time, real-time, streaming |\n\n**8. Big Data Usages:**\n\n* Human interaction, Business processes, Knowledge discovery, Enterprise applications.\n\n### Scalability and Parallel Processing:\n\n* Big data processing requires massive computational power and utilizes parallel processing using hundreds of nodes.\n* **Convergence of Data Environments and Analytics:**  Big data processing and analytics require scaling up (vertical) and scaling out (horizontal) computing resources.\n* **Analytics Scalability:**\n    * **Vertical Scaling:** Increasing resources of a single system.\n    * **Horizontal Scaling:** Adding more systems to distribute workload.\n* **Massively Parallel Processing (MPP):**  Distributing tasks across multiple CPUs or computers for faster processing.\n* **Distributed Computing Model:** Using cloud, grid, or clusters to process big datasets on distributed nodes.\n* **Cloud Computing:** Internet-based computing providing shared processing resources and data on demand. Three main types:\n    * **Infrastructure as a Service (IaaS):** Access to resources like hard disks, networks, and virtual servers.\n    * **Platform as a Service (PaaS):** Runtime environment for building and deploying applications.\n    * **Software as a Service (SaaS):**  Software applications delivered over the internet.\n* **Grid Computing:** Distributed computing where geographically dispersed computers work together to achieve a common task.\n* **Cluster Computing:** A group of interconnected computers working together to perform a task, often used for load balancing.\n* **Volunteer Computing:** Utilizing computing resources from volunteers for distributed computing projects.\n\n\n### Designing Data Architecture:\n\n* **Big Data Architecture:** Logical/physical layout of how big data is stored, accessed, and managed. It defines the components, information flow, and security.\n* **Five Logical Layers:**\n    1. Identification of data sources.\n    2. Data acquisition, ingestion, pre-processing, and transformation.\n    3. Data storage (files, servers, cluster, cloud).\n    4. Data processing.\n    5. Data consumption (BI, data mining, AI, ML, etc.).\n\n### Data Quality and Pre-processing:\n\n* **Data Quality (5 Rs):**\n    * Relevancy\n    * Recency\n    * Range\n    * Robustness\n    * Reliability\n* **Data Integrity:** Maintaining consistency and accuracy of data.\n* **Data Noise:** Meaningless information affecting data analysis.\n* **Outliers:** Data points outside the expected range.\n* **Missing Values:** Data not present in the dataset.\n* **Duplicate Values:** Data appearing multiple times in the dataset.\n* **Data Pre-processing:** Essential step before data mining and analytics. Includes:\n    * Handling missing values, outliers, and inconsistencies.\n    * Filtering unreliable or redundant information.\n    * Data cleaning, editing, reduction, wrangling, validation, transformation, and transcoding.\n    * ETL (Extract, Transform, Load) processing.\n\n### Big Data Platform:\n\n* Supports large datasets, high velocity, variety, and veracity.\n* Requires significant resources (MPPs, cloud, parallel processing).\n* Provides tools and services for storage, processing, analytics, environment management, application integration, etc.\n\n### Hadoop:\n\n* Open-source framework for distributed processing of large datasets.\n* **Ecosystem Components:**\n    * HDFS (Hadoop Distributed File System)\n    * MapReduce (Programming model)\n    * YARN (Yet Another Resource Negotiator)\n    * Zookeeper (Coordination service)\n    * Pig (High-level data flow language)\n    * Hive (Data warehouse infrastructure)\n    * Sqoop (Data transfer tool between Hadoop and RDBMS)\n    * Flume (Data collection and aggregation tool)\n    * Oozie (Workflow scheduler)\n    * HBase (NoSQL database)\n\n\n### Module 2 focuses on Hadoop and its ecosystem in greater detail, covering HDFS, MapReduce, YARN, and several essential Hadoop tools like Pig, Hive, Sqoop, Flume, Oozie, and HBase. It explains their architecture, functionalities, and usage with examples and diagrams.\n\n\nThis detailed analysis provides a structured overview of the provided notes on Big Data Analytics.  Each section highlights the key concepts, formulas (where applicable), and important points, organized by topics and subtopics for improved readability and understanding.\n```", "processed_at": "2025-04-12T10:48:57.712414", "chunk_ids": [], "chunks_count": 0}, {"file_path": "data\\temp\\upload_eb418dd9\\@vtucode.in-21CS71-module-2-pdf.pdf", "file_name": "@vtucode.in-21CS71-module-2-pdf.pdf", "extracted_text": "```\n## Analysis of Big Data Analytics Notes\n\nThis document provides an overview of Big Data Analytics, covering its need, characteristics, types, architecture, processing, tools, and applications.\n\n### Module 1: Introduction to Big Data Analytics\n\n**1. Need for Big Data:**\n\n* The increase in technology has resulted in massive data production and storage, moving from megabytes to petabytes.\n* Conventional systems struggle with the volume, variety, velocity, and complexity of this data.\n* Big data analytics addresses the need for quick processing, analysis, and utilization of this data.\n* Data growth is visualized as a triangle with increasing size and complexity leading to a higher proportion of unstructured data.\n\n**2. Data:**\n\n* Definitions:\n    * Information (facts or statistics) for analysis and calculations.\n    * Information stored and used by computer programs.\n    * Information represented in numbers, letters, or other forms.\n    * Information from observations, measurements, or facts.\n* Web Data: Data on web servers (text, images, videos, etc.) accessed by users (clients) through interactions like pulling, pushing, and posting data. Sources include websites, web services, emails, social media, etc.\n\n\n**3. Data Types:**\n\n* **Structured Data:** Conforms to schemas and models (e.g., tables). Enables operations like insert, delete, update, append, indexing, scaling, and encryption.\n* **Semi-structured Data:** Contains tags/markers that separate semantic elements and create hierarchies (e.g., XML, JSON). Does not conform to formal data models.\n* **Multi-structured Data:** Consists of multiple data formats (structured, semi-structured, unstructured) found in non-transactional systems.\n* **Unstructured Data:** Lacks predefined structure (e.g., text files, CSV). Relationships, schema, and features need to be established.\n\n**4. Big Data:**\n\n* Definition:  High-volume, high-velocity, and/or high-variety information asset requiring new processing forms for enhanced decision-making and insight discovery.\n* Other definitions highlight the challenges of processing and managing such large datasets with traditional tools.\n\n**5. Big Data Characteristics (4Vs):**\n\n* **Volume:**  The amount of data generated.\n* **Velocity:** The speed of data generation and processing.\n* **Variety:** Different forms and formats of data.\n* **Veracity:**  The quality and accuracy of data.\n\n**6. Big Data Types:**\n\n* Social media and web data\n* Transactional and business process data\n* Customer master data\n* Machine-generated data\n* Human-generated data\n\n**7. Big Data Classification Table:**\n\n| Basis of Classification | Examples |\n|---|---|\n| Data sources (traditional) | Records, RDBMS, data warehouses |\n| Data formats (traditional) | Structured, semi-structured |\n| Big Data sources | NoSQL databases, sensor data, web data |\n| Big Data formats | Unstructured, semi-structured, multi-structured |\n| Data Stores structure | Row-oriented, column-oriented, graph databases |\n| Processing data rates | Batch, near-time, real-time, streaming |\n\n**8. Big Data Usages:**\n\n* Human interaction, Business processes, Knowledge discovery, Enterprise applications.\n\n### Scalability and Parallel Processing:\n\n* Big data processing requires massive computational power and utilizes parallel processing using hundreds of nodes.\n* **Convergence of Data Environments and Analytics:**  Big data processing and analytics require scaling up (vertical) and scaling out (horizontal) computing resources.\n* **Analytics Scalability:**\n    * **Vertical Scaling:** Increasing resources of a single system.\n    * **Horizontal Scaling:** Adding more systems to distribute workload.\n* **Massively Parallel Processing (MPP):**  Distributing tasks across multiple CPUs or computers for faster processing.\n* **Distributed Computing Model:** Using cloud, grid, or clusters to process big datasets on distributed nodes.\n* **Cloud Computing:** Internet-based computing providing shared processing resources and data on demand. Three main types:\n    * **Infrastructure as a Service (IaaS):** Access to resources like hard disks, networks, and virtual servers.\n    * **Platform as a Service (PaaS):** Runtime environment for building and deploying applications.\n    * **Software as a Service (SaaS):**  Software applications delivered over the internet.\n* **Grid Computing:** Distributed computing where geographically dispersed computers work together to achieve a common task.\n* **Cluster Computing:** A group of interconnected computers working together to perform a task, often used for load balancing.\n* **Volunteer Computing:** Utilizing computing resources from volunteers for distributed computing projects.\n\n\n### Designing Data Architecture:\n\n* **Big Data Architecture:** Logical/physical layout of how big data is stored, accessed, and managed. It defines the components, information flow, and security.\n* **Five Logical Layers:**\n    1. Identification of data sources.\n    2. Data acquisition, ingestion, pre-processing, and transformation.\n    3. Data storage (files, servers, cluster, cloud).\n    4. Data processing.\n    5. Data consumption (BI, data mining, AI, ML, etc.).\n\n### Data Quality and Pre-processing:\n\n* **Data Quality (5 Rs):**\n    * Relevancy\n    * Recency\n    * Range\n    * Robustness\n    * Reliability\n* **Data Integrity:** Maintaining consistency and accuracy of data.\n* **Data Noise:** Meaningless information affecting data analysis.\n* **Outliers:** Data points outside the expected range.\n* **Missing Values:** Data not present in the dataset.\n* **Duplicate Values:** Data appearing multiple times in the dataset.\n* **Data Pre-processing:** Essential step before data mining and analytics. Includes:\n    * Handling missing values, outliers, and inconsistencies.\n    * Filtering unreliable or redundant information.\n    * Data cleaning, editing, reduction, wrangling, validation, transformation, and transcoding.\n    * ETL (Extract, Transform, Load) processing.\n\n### Big Data Platform:\n\n* Supports large datasets, high velocity, variety, and veracity.\n* Requires significant resources (MPPs, cloud, parallel processing).\n* Provides tools and services for storage, processing, analytics, environment management, application integration, etc.\n\n### Hadoop:\n\n* Open-source framework for distributed processing of large datasets.\n* **Ecosystem Components:**\n    * HDFS (Hadoop Distributed File System)\n    * MapReduce (Programming model)\n    * YARN (Yet Another Resource Negotiator)\n    * Zookeeper (Coordination service)\n    * Pig (High-level data flow language)\n    * Hive (Data warehouse infrastructure)\n    * Sqoop (Data transfer tool between Hadoop and RDBMS)\n    * Flume (Data collection and aggregation tool)\n    * Oozie (Workflow scheduler)\n    * HBase (NoSQL database)\n\n\n### Module 2 focuses on Hadoop and its ecosystem in greater detail, covering HDFS, MapReduce, YARN, and several essential Hadoop tools like Pig, Hive, Sqoop, Flume, Oozie, and HBase. It explains their architecture, functionalities, and usage with examples and diagrams.\n\n\nThis detailed analysis provides a structured overview of the provided notes on Big Data Analytics.  Each section highlights the key concepts, formulas (where applicable), and important points, organized by topics and subtopics for improved readability and understanding.\n```", "processed_at": "2025-04-12T10:48:57.712414", "chunk_ids": [], "chunks_count": 0}]